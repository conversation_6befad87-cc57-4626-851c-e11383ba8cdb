"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Please select a valid class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Academic year ID must be a valid UUID').optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, trigger } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                generateStudentIdPreview();\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            console.log('Fetching classes...');\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            console.log('Classes API response:', response);\n            console.log('Response data:', response.data);\n            console.log('Response data type:', typeof response.data);\n            console.log('Is response.data an array?', Array.isArray(response.data));\n            if (response.success && response.data) {\n                // Check if response.data is an array or if classes are nested deeper\n                let classesArray = response.data;\n                // Handle different possible response structures\n                if (response.data.classes && Array.isArray(response.data.classes)) {\n                    classesArray = response.data.classes;\n                    console.log('Found classes in response.data.classes:', classesArray);\n                } else if (response.data.data && Array.isArray(response.data.data)) {\n                    classesArray = response.data.data;\n                    console.log('Found classes in response.data.data:', classesArray);\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                    console.log('Found classes directly in response.data:', classesArray);\n                } else {\n                    console.warn('Unexpected response structure:', response.data);\n                    classesArray = [];\n                }\n                console.log('Setting classes array:', classesArray);\n                console.log('Classes array length:', classesArray.length);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                console.error('Failed to fetch classes:', response.message);\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>(cls.id || cls.uuid) === data.currentClassId);\n            await onAdd(data);\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Student Added Successfully! 🎉\",\n                description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: \"Add New Student\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: \"Enter the student information below. Click save when you're done.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 59\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                onValueChange: (value)=>setValue('currentClassId', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.currentClassId ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: loadingClasses ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"loading\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Loading classes...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this) : classes.length > 0 ? classes.map((cls)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: cls.id || cls.uuid,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: cls.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        cls.grade_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"Grade: \",\n                                                                                cls.grade_level\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        cls.capacity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"Capacity: \",\n                                                                                cls.capacity\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, cls.id || cls.uuid, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                            value: \"no-classes\",\n                                                            disabled: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"⚠️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"No classes available\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                defaultValue: \"male\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Adding...\"\n                                            ]\n                                        }, void 0, true) : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"s/0LCcRtUjKyrAeBA8OVBJGwKBY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});