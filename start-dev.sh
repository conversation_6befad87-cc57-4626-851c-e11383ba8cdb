#!/bin/bash

echo "Starting School Management System Development Environment"
echo ""

echo "Starting Backend Server..."
cd backend
npm run dev &
BACKEND_PID=$!

echo "Waiting 5 seconds for backend to initialize..."
sleep 5

echo "Starting Frontend Development Server..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "Both servers are running!"
echo ""
echo "Backend: http://localhost:5000"
echo "Frontend: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop both servers"

# Function to cleanup processes
cleanup() {
    echo ""
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "Servers stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for processes
wait
