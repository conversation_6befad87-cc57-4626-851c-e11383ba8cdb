"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-table/build/lib/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.GlobalFiltering),\n/* harmony export */   Headers: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.Headers),\n/* harmony export */   RowExpanding: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowExpanding),\n/* harmony export */   RowPagination: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPagination),\n/* harmony export */   RowPinning: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowPinning),\n/* harmony export */   RowSelection: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSelection),\n/* harmony export */   RowSorting: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__._getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.buildHeaderGroups),\n/* harmony export */   createCell: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createCell),\n/* harmony export */   createColumn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumn),\n/* harmony export */   createColumnHelper: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createColumnHelper),\n/* harmony export */   createRow: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createRow),\n/* harmony export */   createTable: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable),\n/* harmony export */   defaultColumnSizing: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.expandRows),\n/* harmony export */   filterFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.filterFns),\n/* harmony export */   flattenBy: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.flattenBy),\n/* harmony export */   flexRender: () => (/* binding */ flexRender),\n/* harmony export */   functionalUpdate: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.getSortedRowModel),\n/* harmony export */   isFunction: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isFunction),\n/* harmony export */   isNumberArray: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isNumberArray),\n/* harmony export */   isRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.makeStateUpdater),\n/* harmony export */   memo: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.memo),\n/* harmony export */   noop: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.noop),\n/* harmony export */   orderColumns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* reexport safe */ _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.sortingFns),\n/* harmony export */   useReactTable: () => (/* binding */ useReactTable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/table-core */ \"(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\");\n/**\n   * react-table\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n\n\n\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nfunction flexRender(Comp, props) {\n  return !Comp ? null : isReactComponent(Comp) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Comp, props) : Comp;\n}\nfunction isReactComponent(component) {\n  return isClassComponent(component) || typeof component === 'function' || isExoticComponent(component);\n}\nfunction isClassComponent(component) {\n  return typeof component === 'function' && (() => {\n    const proto = Object.getPrototypeOf(component);\n    return proto.prototype && proto.prototype.isReactComponent;\n  })();\n}\nfunction isExoticComponent(component) {\n  return typeof component === 'object' && typeof component.$$typeof === 'symbol' && ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description);\n}\nfunction useReactTable(options) {\n  // Compose in the generic options to the user options\n  const resolvedOptions = {\n    state: {},\n    // Dummy state\n    onStateChange: () => {},\n    // noop\n    renderFallbackValue: null,\n    ...options\n  };\n\n  // Create a new table and store it in state\n  const [tableRef] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => ({\n    current: (0,_tanstack_table_core__WEBPACK_IMPORTED_MODULE_1__.createTable)(resolvedOptions)\n  }));\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => tableRef.current.initialState);\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater);\n      options.onStateChange == null || options.onStateChange(updater);\n    }\n  }));\n  return tableRef.current;\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-table/build/lib/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/table-core/build/lib/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnFaceting: () => (/* binding */ ColumnFaceting),\n/* harmony export */   ColumnFiltering: () => (/* binding */ ColumnFiltering),\n/* harmony export */   ColumnGrouping: () => (/* binding */ ColumnGrouping),\n/* harmony export */   ColumnOrdering: () => (/* binding */ ColumnOrdering),\n/* harmony export */   ColumnPinning: () => (/* binding */ ColumnPinning),\n/* harmony export */   ColumnSizing: () => (/* binding */ ColumnSizing),\n/* harmony export */   ColumnVisibility: () => (/* binding */ ColumnVisibility),\n/* harmony export */   GlobalFaceting: () => (/* binding */ GlobalFaceting),\n/* harmony export */   GlobalFiltering: () => (/* binding */ GlobalFiltering),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   RowExpanding: () => (/* binding */ RowExpanding),\n/* harmony export */   RowPagination: () => (/* binding */ RowPagination),\n/* harmony export */   RowPinning: () => (/* binding */ RowPinning),\n/* harmony export */   RowSelection: () => (/* binding */ RowSelection),\n/* harmony export */   RowSorting: () => (/* binding */ RowSorting),\n/* harmony export */   _getVisibleLeafColumns: () => (/* binding */ _getVisibleLeafColumns),\n/* harmony export */   aggregationFns: () => (/* binding */ aggregationFns),\n/* harmony export */   buildHeaderGroups: () => (/* binding */ buildHeaderGroups),\n/* harmony export */   createCell: () => (/* binding */ createCell),\n/* harmony export */   createColumn: () => (/* binding */ createColumn),\n/* harmony export */   createColumnHelper: () => (/* binding */ createColumnHelper),\n/* harmony export */   createRow: () => (/* binding */ createRow),\n/* harmony export */   createTable: () => (/* binding */ createTable),\n/* harmony export */   defaultColumnSizing: () => (/* binding */ defaultColumnSizing),\n/* harmony export */   expandRows: () => (/* binding */ expandRows),\n/* harmony export */   filterFns: () => (/* binding */ filterFns),\n/* harmony export */   flattenBy: () => (/* binding */ flattenBy),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   getCoreRowModel: () => (/* binding */ getCoreRowModel),\n/* harmony export */   getExpandedRowModel: () => (/* binding */ getExpandedRowModel),\n/* harmony export */   getFacetedMinMaxValues: () => (/* binding */ getFacetedMinMaxValues),\n/* harmony export */   getFacetedRowModel: () => (/* binding */ getFacetedRowModel),\n/* harmony export */   getFacetedUniqueValues: () => (/* binding */ getFacetedUniqueValues),\n/* harmony export */   getFilteredRowModel: () => (/* binding */ getFilteredRowModel),\n/* harmony export */   getGroupedRowModel: () => (/* binding */ getGroupedRowModel),\n/* harmony export */   getMemoOptions: () => (/* binding */ getMemoOptions),\n/* harmony export */   getPaginationRowModel: () => (/* binding */ getPaginationRowModel),\n/* harmony export */   getSortedRowModel: () => (/* binding */ getSortedRowModel),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNumberArray: () => (/* binding */ isNumberArray),\n/* harmony export */   isRowSelected: () => (/* binding */ isRowSelected),\n/* harmony export */   isSubRowSelected: () => (/* binding */ isSubRowSelected),\n/* harmony export */   makeStateUpdater: () => (/* binding */ makeStateUpdater),\n/* harmony export */   memo: () => (/* binding */ memo),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   orderColumns: () => (/* binding */ orderColumns),\n/* harmony export */   passiveEventSupported: () => (/* binding */ passiveEventSupported),\n/* harmony export */   reSplitAlphaNumeric: () => (/* binding */ reSplitAlphaNumeric),\n/* harmony export */   selectRowsFn: () => (/* binding */ selectRowsFn),\n/* harmony export */   shouldAutoRemoveFilter: () => (/* binding */ shouldAutoRemoveFilter),\n/* harmony export */   sortingFns: () => (/* binding */ sortingFns)\n/* harmony export */ });\n/**\n   * table-core\n   *\n   * Copyright (c) TanStack\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE.md file in the root directory of this source tree.\n   *\n   * @license MIT\n   */\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nfunction createColumnHelper() {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function' ? {\n        ...column,\n        accessorFn: accessor\n      } : {\n        ...column,\n        accessorKey: accessor\n      };\n    },\n    display: column => column,\n    group: column => column\n  };\n}\n\n// Is this type a tuple?\n\n// If this type is a tuple, what indices are allowed?\n\n///\n\nfunction functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nfunction noop() {\n  //\n}\nfunction makeStateUpdater(key, instance) {\n  return updater => {\n    instance.setState(old => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, old[key])\n      };\n    });\n  };\n}\nfunction isFunction(d) {\n  return d instanceof Function;\n}\nfunction isNumberArray(d) {\n  return Array.isArray(d) && d.every(val => typeof val === 'number');\n}\nfunction flattenBy(arr, getChildren) {\n  const flat = [];\n  const recurse = subArr => {\n    subArr.forEach(item => {\n      flat.push(item);\n      const children = getChildren(item);\n      if (children != null && children.length) {\n        recurse(children);\n      }\n    });\n  };\n  recurse(arr);\n  return flat;\n}\nfunction memo(getDeps, fn, opts) {\n  let deps = [];\n  let result;\n  return depArgs => {\n    let depTime;\n    if (opts.key && opts.debug) depTime = Date.now();\n    const newDeps = getDeps(depArgs);\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && opts.debug) resultTime = Date.now();\n    result = fn(...newDeps);\n    opts == null || opts.onChange == null || opts.onChange(result);\n    if (opts.key && opts.debug) {\n      if (opts != null && opts.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n        const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n        const resultFpsPercentage = resultEndTime / 16;\n        const pad = (str, num) => {\n          str = String(str);\n          while (str.length < num) {\n            str = ' ' + str;\n          }\n          return str;\n        };\n        console.info(`%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`, `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120))}deg 100% 31%);`, opts == null ? void 0 : opts.key);\n      }\n    }\n    return result;\n  };\n}\nfunction getMemoOptions(tableOptions, debugLevel, key, onChange) {\n  return {\n    debug: () => {\n      var _tableOptions$debugAl;\n      return (_tableOptions$debugAl = tableOptions == null ? void 0 : tableOptions.debugAll) != null ? _tableOptions$debugAl : tableOptions[debugLevel];\n    },\n    key:  true && key,\n    onChange\n  };\n}\n\nfunction createCell(table, row, column, columnId) {\n  const getRenderValue = () => {\n    var _cell$getValue;\n    return (_cell$getValue = cell.getValue()) != null ? _cell$getValue : table.options.renderFallbackValue;\n  };\n  const cell = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(() => [table, column, row, cell], (table, column, row, cell) => ({\n      table,\n      column,\n      row,\n      cell: cell,\n      getValue: cell.getValue,\n      renderValue: cell.renderValue\n    }), getMemoOptions(table.options, 'debugCells', 'cell.getContext'))\n  };\n  table._features.forEach(feature => {\n    feature.createCell == null || feature.createCell(cell, column, row, table);\n  }, {});\n  return cell;\n}\n\nfunction createColumn(table, columnDef, depth, parent) {\n  var _ref, _resolvedColumnDef$id;\n  const defaultColumn = table._getDefaultColumnDef();\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef\n  };\n  const accessorKey = resolvedColumnDef.accessorKey;\n  let id = (_ref = (_resolvedColumnDef$id = resolvedColumnDef.id) != null ? _resolvedColumnDef$id : accessorKey ? typeof String.prototype.replaceAll === 'function' ? accessorKey.replaceAll('.', '_') : accessorKey.replace(/\\./g, '_') : undefined) != null ? _ref : typeof resolvedColumnDef.header === 'string' ? resolvedColumnDef.header : undefined;\n  let accessorFn;\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn;\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = originalRow => {\n        let result = originalRow;\n        for (const key of accessorKey.split('.')) {\n          var _result;\n          result = (_result = result) == null ? void 0 : _result[key];\n          if ( true && result === undefined) {\n            console.warn(`\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`);\n          }\n        }\n        return result;\n      };\n    } else {\n      accessorFn = originalRow => originalRow[resolvedColumnDef.accessorKey];\n    }\n  }\n  if (!id) {\n    if (true) {\n      throw new Error(resolvedColumnDef.accessorFn ? `Columns require an id when using an accessorFn` : `Columns require an id when using a non-string header`);\n    }\n    throw new Error();\n  }\n  let column = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent,\n    depth,\n    columnDef: resolvedColumnDef,\n    columns: [],\n    getFlatColumns: memo(() => [true], () => {\n      var _column$columns;\n      return [column, ...((_column$columns = column.columns) == null ? void 0 : _column$columns.flatMap(d => d.getFlatColumns()))];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getFlatColumns')),\n    getLeafColumns: memo(() => [table._getOrderColumnsFn()], orderColumns => {\n      var _column$columns2;\n      if ((_column$columns2 = column.columns) != null && _column$columns2.length) {\n        let leafColumns = column.columns.flatMap(column => column.getLeafColumns());\n        return orderColumns(leafColumns);\n      }\n      return [column];\n    }, getMemoOptions(table.options, 'debugColumns', 'column.getLeafColumns'))\n  };\n  for (const feature of table._features) {\n    feature.createColumn == null || feature.createColumn(column, table);\n  }\n\n  // Yes, we have to convert table to unknown, because we know more than the compiler here.\n  return column;\n}\n\nconst debug = 'debugHeaders';\n//\n\nfunction createHeader(table, column, options) {\n  var _options$id;\n  const id = (_options$id = options.id) != null ? _options$id : column.id;\n  let header = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null,\n    getLeafHeaders: () => {\n      const leafHeaders = [];\n      const recurseHeader = h => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader);\n        }\n        leafHeaders.push(h);\n      };\n      recurseHeader(header);\n      return leafHeaders;\n    },\n    getContext: () => ({\n      table,\n      header: header,\n      column\n    })\n  };\n  table._features.forEach(feature => {\n    feature.createHeader == null || feature.createHeader(header, table);\n  });\n  return header;\n}\nconst Headers = {\n  createTable: table => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      var _left$map$filter, _right$map$filter;\n      const leftColumns = (_left$map$filter = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter : [];\n      const rightColumns = (_right$map$filter = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter : [];\n      const centerColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      const headerGroups = buildHeaderGroups(allColumns, [...leftColumns, ...centerColumns, ...rightColumns], table);\n      return headerGroups;\n    }, getMemoOptions(table.options, debug, 'getHeaderGroups'));\n    table.getCenterHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, leafColumns, left, right) => {\n      leafColumns = leafColumns.filter(column => !(left != null && left.includes(column.id)) && !(right != null && right.includes(column.id)));\n      return buildHeaderGroups(allColumns, leafColumns, table, 'center');\n    }, getMemoOptions(table.options, debug, 'getCenterHeaderGroups'));\n    table.getLeftHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.left], (allColumns, leafColumns, left) => {\n      var _left$map$filter2;\n      const orderedLeafColumns = (_left$map$filter2 = left == null ? void 0 : left.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _left$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left');\n    }, getMemoOptions(table.options, debug, 'getLeftHeaderGroups'));\n    table.getRightHeaderGroups = memo(() => [table.getAllColumns(), table.getVisibleLeafColumns(), table.getState().columnPinning.right], (allColumns, leafColumns, right) => {\n      var _right$map$filter2;\n      const orderedLeafColumns = (_right$map$filter2 = right == null ? void 0 : right.map(columnId => leafColumns.find(d => d.id === columnId)).filter(Boolean)) != null ? _right$map$filter2 : [];\n      return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right');\n    }, getMemoOptions(table.options, debug, 'getRightHeaderGroups'));\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getFooterGroups'));\n    table.getLeftFooterGroups = memo(() => [table.getLeftHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getLeftFooterGroups'));\n    table.getCenterFooterGroups = memo(() => [table.getCenterHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getCenterFooterGroups'));\n    table.getRightFooterGroups = memo(() => [table.getRightHeaderGroups()], headerGroups => {\n      return [...headerGroups].reverse();\n    }, getMemoOptions(table.options, debug, 'getRightFooterGroups'));\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(() => [table.getHeaderGroups()], headerGroups => {\n      return headerGroups.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getFlatHeaders'));\n    table.getLeftFlatHeaders = memo(() => [table.getLeftHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeftFlatHeaders'));\n    table.getCenterFlatHeaders = memo(() => [table.getCenterHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getCenterFlatHeaders'));\n    table.getRightFlatHeaders = memo(() => [table.getRightHeaderGroups()], left => {\n      return left.map(headerGroup => {\n        return headerGroup.headers;\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getRightFlatHeaders'));\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(() => [table.getCenterFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders;\n        return !((_header$subHeaders = header.subHeaders) != null && _header$subHeaders.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getCenterLeafHeaders'));\n    table.getLeftLeafHeaders = memo(() => [table.getLeftFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders2;\n        return !((_header$subHeaders2 = header.subHeaders) != null && _header$subHeaders2.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getLeftLeafHeaders'));\n    table.getRightLeafHeaders = memo(() => [table.getRightFlatHeaders()], flatHeaders => {\n      return flatHeaders.filter(header => {\n        var _header$subHeaders3;\n        return !((_header$subHeaders3 = header.subHeaders) != null && _header$subHeaders3.length);\n      });\n    }, getMemoOptions(table.options, debug, 'getRightLeafHeaders'));\n    table.getLeafHeaders = memo(() => [table.getLeftHeaderGroups(), table.getCenterHeaderGroups(), table.getRightHeaderGroups()], (left, center, right) => {\n      var _left$0$headers, _left$, _center$0$headers, _center$, _right$0$headers, _right$;\n      return [...((_left$0$headers = (_left$ = left[0]) == null ? void 0 : _left$.headers) != null ? _left$0$headers : []), ...((_center$0$headers = (_center$ = center[0]) == null ? void 0 : _center$.headers) != null ? _center$0$headers : []), ...((_right$0$headers = (_right$ = right[0]) == null ? void 0 : _right$.headers) != null ? _right$0$headers : [])].map(header => {\n        return header.getLeafHeaders();\n      }).flat();\n    }, getMemoOptions(table.options, debug, 'getLeafHeaders'));\n  }\n};\nfunction buildHeaderGroups(allColumns, columnsToGroup, table, headerFamily) {\n  var _headerGroups$0$heade, _headerGroups$;\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0;\n  const findMaxDepth = function (columns, depth) {\n    if (depth === void 0) {\n      depth = 1;\n    }\n    maxDepth = Math.max(maxDepth, depth);\n    columns.filter(column => column.getIsVisible()).forEach(column => {\n      var _column$columns;\n      if ((_column$columns = column.columns) != null && _column$columns.length) {\n        findMaxDepth(column.columns, depth + 1);\n      }\n    }, 0);\n  };\n  findMaxDepth(allColumns);\n  let headerGroups = [];\n  const createHeaderGroup = (headersToGroup, depth) => {\n    // The header group we are creating\n    const headerGroup = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: []\n    };\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders = [];\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0];\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth;\n      let column;\n      let isPlaceholder = false;\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent;\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column;\n        isPlaceholder = true;\n      }\n      if (latestPendingParentHeader && (latestPendingParentHeader == null ? void 0 : latestPendingParentHeader.column) === column) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup);\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup == null ? void 0 : headerToGroup.id].filter(Boolean).join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder ? `${pendingParentHeaders.filter(d => d.column === column).length}` : undefined,\n          depth,\n          index: pendingParentHeaders.length\n        });\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup);\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header);\n      }\n      headerGroup.headers.push(headerToGroup);\n      headerToGroup.headerGroup = headerGroup;\n    });\n    headerGroups.push(headerGroup);\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1);\n    }\n  };\n  const bottomHeaders = columnsToGroup.map((column, index) => createHeader(table, column, {\n    depth: maxDepth,\n    index\n  }));\n  createHeaderGroup(bottomHeaders, maxDepth - 1);\n  headerGroups.reverse();\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = headers => {\n    const filteredHeaders = headers.filter(header => header.column.getIsVisible());\n    return filteredHeaders.map(header => {\n      let colSpan = 0;\n      let rowSpan = 0;\n      let childRowSpans = [0];\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = [];\n        recurseHeadersForSpans(header.subHeaders).forEach(_ref => {\n          let {\n            colSpan: childColSpan,\n            rowSpan: childRowSpan\n          } = _ref;\n          colSpan += childColSpan;\n          childRowSpans.push(childRowSpan);\n        });\n      } else {\n        colSpan = 1;\n      }\n      const minChildRowSpan = Math.min(...childRowSpans);\n      rowSpan = rowSpan + minChildRowSpan;\n      header.colSpan = colSpan;\n      header.rowSpan = rowSpan;\n      return {\n        colSpan,\n        rowSpan\n      };\n    });\n  };\n  recurseHeadersForSpans((_headerGroups$0$heade = (_headerGroups$ = headerGroups[0]) == null ? void 0 : _headerGroups$.headers) != null ? _headerGroups$0$heade : []);\n  return headerGroups;\n}\n\nconst createRow = (table, id, original, rowIndex, depth, subRows, parentId) => {\n  let row = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      row._valuesCache[columnId] = column.accessorFn(row.original, rowIndex);\n      return row._valuesCache[columnId];\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.accessorFn)) {\n        return undefined;\n      }\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)];\n        return row._uniqueValuesCache[columnId];\n      }\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(row.original, rowIndex);\n      return row._uniqueValuesCache[columnId];\n    },\n    renderValue: columnId => {\n      var _row$getValue;\n      return (_row$getValue = row.getValue(columnId)) != null ? _row$getValue : table.options.renderFallbackValue;\n    },\n    subRows: subRows != null ? subRows : [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () => row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows = [];\n      let currentRow = row;\n      while (true) {\n        const parentRow = currentRow.getParentRow();\n        if (!parentRow) break;\n        parentRows.push(parentRow);\n        currentRow = parentRow;\n      }\n      return parentRows.reverse();\n    },\n    getAllCells: memo(() => [table.getAllLeafColumns()], leafColumns => {\n      return leafColumns.map(column => {\n        return createCell(table, row, column, column.id);\n      });\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCells')),\n    _getAllCellsByColumnId: memo(() => [row.getAllCells()], allCells => {\n      return allCells.reduce((acc, cell) => {\n        acc[cell.column.id] = cell;\n        return acc;\n      }, {});\n    }, getMemoOptions(table.options, 'debugRows', 'getAllCellsByColumnId'))\n  };\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i];\n    feature == null || feature.createRow == null || feature.createRow(row, table);\n  }\n  return row;\n};\n\n//\n\nconst ColumnFaceting = {\n  createColumn: (column, table) => {\n    column._getFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, column.id);\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return column._getFacetedRowModel();\n    };\n    column._getFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, column.id);\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map();\n      }\n      return column._getFacetedUniqueValues();\n    };\n    column._getFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, column.id);\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined;\n      }\n      return column._getFacetedMinMaxValues();\n    };\n  }\n};\n\nconst includesString = (row, columnId, filterValue) => {\n  var _filterValue$toString, _row$getValue;\n  const search = filterValue == null || (_filterValue$toString = filterValue.toString()) == null ? void 0 : _filterValue$toString.toLowerCase();\n  return Boolean((_row$getValue = row.getValue(columnId)) == null || (_row$getValue = _row$getValue.toString()) == null || (_row$getValue = _row$getValue.toLowerCase()) == null ? void 0 : _row$getValue.includes(search));\n};\nincludesString.autoRemove = val => testFalsey(val);\nconst includesStringSensitive = (row, columnId, filterValue) => {\n  var _row$getValue2;\n  return Boolean((_row$getValue2 = row.getValue(columnId)) == null || (_row$getValue2 = _row$getValue2.toString()) == null ? void 0 : _row$getValue2.includes(filterValue));\n};\nincludesStringSensitive.autoRemove = val => testFalsey(val);\nconst equalsString = (row, columnId, filterValue) => {\n  var _row$getValue3;\n  return ((_row$getValue3 = row.getValue(columnId)) == null || (_row$getValue3 = _row$getValue3.toString()) == null ? void 0 : _row$getValue3.toLowerCase()) === (filterValue == null ? void 0 : filterValue.toLowerCase());\n};\nequalsString.autoRemove = val => testFalsey(val);\nconst arrIncludes = (row, columnId, filterValue) => {\n  var _row$getValue4;\n  return (_row$getValue4 = row.getValue(columnId)) == null ? void 0 : _row$getValue4.includes(filterValue);\n};\narrIncludes.autoRemove = val => testFalsey(val);\nconst arrIncludesAll = (row, columnId, filterValue) => {\n  return !filterValue.some(val => {\n    var _row$getValue5;\n    return !((_row$getValue5 = row.getValue(columnId)) != null && _row$getValue5.includes(val));\n  });\n};\narrIncludesAll.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst arrIncludesSome = (row, columnId, filterValue) => {\n  return filterValue.some(val => {\n    var _row$getValue6;\n    return (_row$getValue6 = row.getValue(columnId)) == null ? void 0 : _row$getValue6.includes(val);\n  });\n};\narrIncludesSome.autoRemove = val => testFalsey(val) || !(val != null && val.length);\nconst equals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) === filterValue;\n};\nequals.autoRemove = val => testFalsey(val);\nconst weakEquals = (row, columnId, filterValue) => {\n  return row.getValue(columnId) == filterValue;\n};\nweakEquals.autoRemove = val => testFalsey(val);\nconst inNumberRange = (row, columnId, filterValue) => {\n  let [min, max] = filterValue;\n  const rowValue = row.getValue(columnId);\n  return rowValue >= min && rowValue <= max;\n};\ninNumberRange.resolveFilterValue = val => {\n  let [unsafeMin, unsafeMax] = val;\n  let parsedMin = typeof unsafeMin !== 'number' ? parseFloat(unsafeMin) : unsafeMin;\n  let parsedMax = typeof unsafeMax !== 'number' ? parseFloat(unsafeMax) : unsafeMax;\n  let min = unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin;\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax;\n  if (min > max) {\n    const temp = min;\n    min = max;\n    max = temp;\n  }\n  return [min, max];\n};\ninNumberRange.autoRemove = val => testFalsey(val) || testFalsey(val[0]) && testFalsey(val[1]);\n\n// Export\n\nconst filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange\n};\n// Utils\n\nfunction testFalsey(val) {\n  return val === undefined || val === null || val === '';\n}\n\n//\n\nconst ColumnFiltering = {\n  getDefaultColumnDef: () => {\n    return {\n      filterFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      columnFilters: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return filterFns.includesString;\n      }\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange;\n      }\n      if (typeof value === 'boolean') {\n        return filterFns.equals;\n      }\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals;\n      }\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes;\n      }\n      return filterFns.weakEquals;\n    };\n    column.getFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      return isFunction(column.columnDef.filterFn) ? column.columnDef.filterFn : column.columnDef.filterFn === 'auto' ? column.getAutoFilterFn() : // @ts-ignore\n      (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[column.columnDef.filterFn]) != null ? _table$options$filter : filterFns[column.columnDef.filterFn];\n    };\n    column.getCanFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2;\n      return ((_column$columnDef$ena = column.columnDef.enableColumnFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnFilters) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && !!column.accessorFn;\n    };\n    column.getIsFiltered = () => column.getFilterIndex() > -1;\n    column.getFilterValue = () => {\n      var _table$getState$colum;\n      return (_table$getState$colum = table.getState().columnFilters) == null || (_table$getState$colum = _table$getState$colum.find(d => d.id === column.id)) == null ? void 0 : _table$getState$colum.value;\n    };\n    column.getFilterIndex = () => {\n      var _table$getState$colum2, _table$getState$colum3;\n      return (_table$getState$colum2 = (_table$getState$colum3 = table.getState().columnFilters) == null ? void 0 : _table$getState$colum3.findIndex(d => d.id === column.id)) != null ? _table$getState$colum2 : -1;\n    };\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn();\n        const previousFilter = old == null ? void 0 : old.find(d => d.id === column.id);\n        const newFilter = functionalUpdate(value, previousFilter ? previousFilter.value : undefined);\n\n        //\n        if (shouldAutoRemoveFilter(filterFn, newFilter, column)) {\n          var _old$filter;\n          return (_old$filter = old == null ? void 0 : old.filter(d => d.id !== column.id)) != null ? _old$filter : [];\n        }\n        const newFilterObj = {\n          id: column.id,\n          value: newFilter\n        };\n        if (previousFilter) {\n          var _old$map;\n          return (_old$map = old == null ? void 0 : old.map(d => {\n            if (d.id === column.id) {\n              return newFilterObj;\n            }\n            return d;\n          })) != null ? _old$map : [];\n        }\n        if (old != null && old.length) {\n          return [...old, newFilterObj];\n        }\n        return [newFilterObj];\n      });\n    };\n  },\n  createRow: (row, _table) => {\n    row.columnFilters = {};\n    row.columnFiltersMeta = {};\n  },\n  createTable: table => {\n    table.setColumnFilters = updater => {\n      const leafColumns = table.getAllLeafColumns();\n      const updateFn = old => {\n        var _functionalUpdate;\n        return (_functionalUpdate = functionalUpdate(updater, old)) == null ? void 0 : _functionalUpdate.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id);\n          if (column) {\n            const filterFn = column.getFilterFn();\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false;\n            }\n          }\n          return true;\n        });\n      };\n      table.options.onColumnFiltersChange == null || table.options.onColumnFiltersChange(updateFn);\n    };\n    table.resetColumnFilters = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      table.setColumnFilters(defaultState ? [] : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnFilters) != null ? _table$initialState$c : []);\n    };\n    table.getPreFilteredRowModel = () => table.getCoreRowModel();\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table);\n      }\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getFilteredRowModel();\n    };\n  }\n};\nfunction shouldAutoRemoveFilter(filterFn, value, column) {\n  return (filterFn && filterFn.autoRemove ? filterFn.autoRemove(value, column) : false) || typeof value === 'undefined' || typeof value === 'string' && !value;\n}\n\nconst sum = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId);\n    return sum + (typeof nextValue === 'number' ? nextValue : 0);\n  }, 0);\n};\nconst min = (columnId, _leafRows, childRows) => {\n  let min;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (min > value || min === undefined && value >= value)) {\n      min = value;\n    }\n  });\n  return min;\n};\nconst max = (columnId, _leafRows, childRows) => {\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null && (max < value || max === undefined && value >= value)) {\n      max = value;\n    }\n  });\n  return max;\n};\nconst extent = (columnId, _leafRows, childRows) => {\n  let min;\n  let max;\n  childRows.forEach(row => {\n    const value = row.getValue(columnId);\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value;\n      } else {\n        if (min > value) min = value;\n        if (max < value) max = value;\n      }\n    }\n  });\n  return [min, max];\n};\nconst mean = (columnId, leafRows) => {\n  let count = 0;\n  let sum = 0;\n  leafRows.forEach(row => {\n    let value = row.getValue(columnId);\n    if (value != null && (value = +value) >= value) {\n      ++count, sum += value;\n    }\n  });\n  if (count) return sum / count;\n  return;\n};\nconst median = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return;\n  }\n  const values = leafRows.map(row => row.getValue(columnId));\n  if (!isNumberArray(values)) {\n    return;\n  }\n  if (values.length === 1) {\n    return values[0];\n  }\n  const mid = Math.floor(values.length / 2);\n  const nums = values.sort((a, b) => a - b);\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1] + nums[mid]) / 2;\n};\nconst unique = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values());\n};\nconst uniqueCount = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size;\n};\nconst count = (_columnId, leafRows) => {\n  return leafRows.length;\n};\nconst aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count\n};\n\n//\n\nconst ColumnGrouping = {\n  getDefaultColumnDef: () => {\n    return {\n      aggregatedCell: props => {\n        var _toString, _props$getValue;\n        return (_toString = (_props$getValue = props.getValue()) == null || _props$getValue.toString == null ? void 0 : _props$getValue.toString()) != null ? _toString : null;\n      },\n      aggregationFn: 'auto'\n    };\n  },\n  getInitialState: state => {\n    return {\n      grouping: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder'\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old != null && old.includes(column.id)) {\n          return old.filter(d => d !== column.id);\n        }\n        return [...(old != null ? old : []), column.id];\n      });\n    };\n    column.getCanGroup = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableGrouping) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGrouping) != null ? _table$options$enable : true) && (!!column.accessorFn || !!column.columnDef.getGroupingValue);\n    };\n    column.getIsGrouped = () => {\n      var _table$getState$group;\n      return (_table$getState$group = table.getState().grouping) == null ? void 0 : _table$getState$group.includes(column.id);\n    };\n    column.getGroupedIndex = () => {\n      var _table$getState$group2;\n      return (_table$getState$group2 = table.getState().grouping) == null ? void 0 : _table$getState$group2.indexOf(column.id);\n    };\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup();\n      return () => {\n        if (!canGroup) return;\n        column.toggleGrouping();\n      };\n    };\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'number') {\n        return aggregationFns.sum;\n      }\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent;\n      }\n    };\n    column.getAggregationFn = () => {\n      var _table$options$aggreg, _table$options$aggreg2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.aggregationFn) ? column.columnDef.aggregationFn : column.columnDef.aggregationFn === 'auto' ? column.getAutoAggregationFn() : (_table$options$aggreg = (_table$options$aggreg2 = table.options.aggregationFns) == null ? void 0 : _table$options$aggreg2[column.columnDef.aggregationFn]) != null ? _table$options$aggreg : aggregationFns[column.columnDef.aggregationFn];\n    };\n  },\n  createTable: table => {\n    table.setGrouping = updater => table.options.onGroupingChange == null ? void 0 : table.options.onGroupingChange(updater);\n    table.resetGrouping = defaultState => {\n      var _table$initialState$g, _table$initialState;\n      table.setGrouping(defaultState ? [] : (_table$initialState$g = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.grouping) != null ? _table$initialState$g : []);\n    };\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel();\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table);\n      }\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel();\n      }\n      return table._getGroupedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.getIsGrouped = () => !!row.groupingColumnId;\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId];\n      }\n      const column = table.getColumn(columnId);\n      if (!(column != null && column.columnDef.getGroupingValue)) {\n        return row.getValue(columnId);\n      }\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(row.original);\n      return row._groupingValuesCache[columnId];\n    };\n    row._groupingValuesCache = {};\n  },\n  createCell: (cell, column, row, table) => {\n    cell.getIsGrouped = () => column.getIsGrouped() && column.id === row.groupingColumnId;\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped();\n    cell.getIsAggregated = () => {\n      var _row$subRows;\n      return !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n  }\n};\nfunction orderColumns(leafColumns, grouping, groupedColumnMode) {\n  if (!(grouping != null && grouping.length) || !groupedColumnMode) {\n    return leafColumns;\n  }\n  const nonGroupingColumns = leafColumns.filter(col => !grouping.includes(col.id));\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns;\n  }\n  const groupingColumns = grouping.map(g => leafColumns.find(col => col.id === g)).filter(Boolean);\n  return [...groupingColumns, ...nonGroupingColumns];\n}\n\n//\n\nconst ColumnOrdering = {\n  getInitialState: state => {\n    return {\n      columnOrder: [],\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getIndex = memo(position => [_getVisibleLeafColumns(table, position)], columns => columns.findIndex(d => d.id === column.id), getMemoOptions(table.options, 'debugColumns', 'getIndex'));\n    column.getIsFirstColumn = position => {\n      var _columns$;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns$ = columns[0]) == null ? void 0 : _columns$.id) === column.id;\n    };\n    column.getIsLastColumn = position => {\n      var _columns;\n      const columns = _getVisibleLeafColumns(table, position);\n      return ((_columns = columns[columns.length - 1]) == null ? void 0 : _columns.id) === column.id;\n    };\n  },\n  createTable: table => {\n    table.setColumnOrder = updater => table.options.onColumnOrderChange == null ? void 0 : table.options.onColumnOrderChange(updater);\n    table.resetColumnOrder = defaultState => {\n      var _table$initialState$c;\n      table.setColumnOrder(defaultState ? [] : (_table$initialState$c = table.initialState.columnOrder) != null ? _table$initialState$c : []);\n    };\n    table._getOrderColumnsFn = memo(() => [table.getState().columnOrder, table.getState().grouping, table.options.groupedColumnMode], (columnOrder, grouping, groupedColumnMode) => columns => {\n      // Sort grouped columns to the start of the column list\n      // before the headers are built\n      let orderedColumns = [];\n\n      // If there is no order, return the normal columns\n      if (!(columnOrder != null && columnOrder.length)) {\n        orderedColumns = columns;\n      } else {\n        const columnOrderCopy = [...columnOrder];\n\n        // If there is an order, make a copy of the columns\n        const columnsCopy = [...columns];\n\n        // And make a new ordered array of the columns\n\n        // Loop over the columns and place them in order into the new array\n        while (columnsCopy.length && columnOrderCopy.length) {\n          const targetColumnId = columnOrderCopy.shift();\n          const foundIndex = columnsCopy.findIndex(d => d.id === targetColumnId);\n          if (foundIndex > -1) {\n            orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]);\n          }\n        }\n\n        // If there are any columns left, add them to the end\n        orderedColumns = [...orderedColumns, ...columnsCopy];\n      }\n      return orderColumns(orderedColumns, grouping, groupedColumnMode);\n    }, getMemoOptions(table.options, 'debugTable', '_getOrderColumnsFn'));\n  }\n};\n\n//\n\nconst getDefaultColumnPinningState = () => ({\n  left: [],\n  right: []\n});\nconst ColumnPinning = {\n  getInitialState: state => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.pin = position => {\n      const columnIds = column.getLeafColumns().map(d => d.id).filter(Boolean);\n      table.setColumnPinning(old => {\n        var _old$left3, _old$right3;\n        if (position === 'right') {\n          var _old$left, _old$right;\n          return {\n            left: ((_old$left = old == null ? void 0 : old.left) != null ? _old$left : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n            right: [...((_old$right = old == null ? void 0 : old.right) != null ? _old$right : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds]\n          };\n        }\n        if (position === 'left') {\n          var _old$left2, _old$right2;\n          return {\n            left: [...((_old$left2 = old == null ? void 0 : old.left) != null ? _old$left2 : []).filter(d => !(columnIds != null && columnIds.includes(d))), ...columnIds],\n            right: ((_old$right2 = old == null ? void 0 : old.right) != null ? _old$right2 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n          };\n        }\n        return {\n          left: ((_old$left3 = old == null ? void 0 : old.left) != null ? _old$left3 : []).filter(d => !(columnIds != null && columnIds.includes(d))),\n          right: ((_old$right3 = old == null ? void 0 : old.right) != null ? _old$right3 : []).filter(d => !(columnIds != null && columnIds.includes(d)))\n        };\n      });\n    };\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns();\n      return leafColumns.some(d => {\n        var _d$columnDef$enablePi, _ref, _table$options$enable;\n        return ((_d$columnDef$enablePi = d.columnDef.enablePinning) != null ? _d$columnDef$enablePi : true) && ((_ref = (_table$options$enable = table.options.enableColumnPinning) != null ? _table$options$enable : table.options.enablePinning) != null ? _ref : true);\n      });\n    };\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id);\n      const {\n        left,\n        right\n      } = table.getState().columnPinning;\n      const isLeft = leafColumnIds.some(d => left == null ? void 0 : left.includes(d));\n      const isRight = leafColumnIds.some(d => right == null ? void 0 : right.includes(d));\n      return isLeft ? 'left' : isRight ? 'right' : false;\n    };\n    column.getPinnedIndex = () => {\n      var _table$getState$colum, _table$getState$colum2;\n      const position = column.getIsPinned();\n      return position ? (_table$getState$colum = (_table$getState$colum2 = table.getState().columnPinning) == null || (_table$getState$colum2 = _table$getState$colum2[position]) == null ? void 0 : _table$getState$colum2.indexOf(column.id)) != null ? _table$getState$colum : -1 : 0;\n    };\n  },\n  createRow: (row, table) => {\n    row.getCenterVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allCells, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allCells.filter(d => !leftAndRight.includes(d.column.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterVisibleCells'));\n    row.getLeftVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.left], (allCells, left) => {\n      const cells = (left != null ? left : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'left'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getLeftVisibleCells'));\n    row.getRightVisibleCells = memo(() => [row._getAllVisibleCells(), table.getState().columnPinning.right], (allCells, right) => {\n      const cells = (right != null ? right : []).map(columnId => allCells.find(cell => cell.column.id === columnId)).filter(Boolean).map(d => ({\n        ...d,\n        position: 'right'\n      }));\n      return cells;\n    }, getMemoOptions(table.options, 'debugRows', 'getRightVisibleCells'));\n  },\n  createTable: table => {\n    table.setColumnPinning = updater => table.options.onColumnPinningChange == null ? void 0 : table.options.onColumnPinningChange(updater);\n    table.resetColumnPinning = defaultState => {\n      var _table$initialState$c, _table$initialState;\n      return table.setColumnPinning(defaultState ? getDefaultColumnPinningState() : (_table$initialState$c = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.columnPinning) != null ? _table$initialState$c : getDefaultColumnPinningState());\n    };\n    table.getIsSomeColumnsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().columnPinning;\n      if (!position) {\n        var _pinningState$left, _pinningState$right;\n        return Boolean(((_pinningState$left = pinningState.left) == null ? void 0 : _pinningState$left.length) || ((_pinningState$right = pinningState.right) == null ? void 0 : _pinningState$right.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table.getLeftLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left], (allColumns, left) => {\n      return (left != null ? left : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getLeftLeafColumns'));\n    table.getRightLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.right], (allColumns, right) => {\n      return (right != null ? right : []).map(columnId => allColumns.find(column => column.id === columnId)).filter(Boolean);\n    }, getMemoOptions(table.options, 'debugColumns', 'getRightLeafColumns'));\n    table.getCenterLeafColumns = memo(() => [table.getAllLeafColumns(), table.getState().columnPinning.left, table.getState().columnPinning.right], (allColumns, left, right) => {\n      const leftAndRight = [...(left != null ? left : []), ...(right != null ? right : [])];\n      return allColumns.filter(d => !leftAndRight.includes(d.id));\n    }, getMemoOptions(table.options, 'debugColumns', 'getCenterLeafColumns'));\n  }\n};\n\nfunction safelyAccessDocument(_document) {\n  return _document || (typeof document !== 'undefined' ? document : null);\n}\n\n//\n\n//\n\nconst defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER\n};\nconst getDefaultColumnSizingInfoState = () => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: []\n});\nconst ColumnSizing = {\n  getDefaultColumnDef: () => {\n    return defaultColumnSizing;\n  },\n  getInitialState: state => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.getSize = () => {\n      var _column$columnDef$min, _ref, _column$columnDef$max;\n      const columnSize = table.getState().columnSizing[column.id];\n      return Math.min(Math.max((_column$columnDef$min = column.columnDef.minSize) != null ? _column$columnDef$min : defaultColumnSizing.minSize, (_ref = columnSize != null ? columnSize : column.columnDef.size) != null ? _ref : defaultColumnSizing.size), (_column$columnDef$max = column.columnDef.maxSize) != null ? _column$columnDef$max : defaultColumnSizing.maxSize);\n    };\n    column.getStart = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(0, column.getIndex(position)).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getStart'));\n    column.getAfter = memo(position => [position, _getVisibleLeafColumns(table, position), table.getState().columnSizing], (position, columns) => columns.slice(column.getIndex(position) + 1).reduce((sum, column) => sum + column.getSize(), 0), getMemoOptions(table.options, 'debugColumns', 'getAfter'));\n    column.resetSize = () => {\n      table.setColumnSizing(_ref2 => {\n        let {\n          [column.id]: _,\n          ...rest\n        } = _ref2;\n        return rest;\n      });\n    };\n    column.getCanResize = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableResizing) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableColumnResizing) != null ? _table$options$enable : true);\n    };\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id;\n    };\n  },\n  createHeader: (header, table) => {\n    header.getSize = () => {\n      let sum = 0;\n      const recurse = header => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse);\n        } else {\n          var _header$column$getSiz;\n          sum += (_header$column$getSiz = header.column.getSize()) != null ? _header$column$getSiz : 0;\n        }\n      };\n      recurse(header);\n      return sum;\n    };\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1];\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize();\n      }\n      return 0;\n    };\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id);\n      const canResize = column == null ? void 0 : column.getCanResize();\n      return e => {\n        if (!column || !canResize) {\n          return;\n        }\n        e.persist == null || e.persist();\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return;\n          }\n        }\n        const startSize = header.getSize();\n        const columnSizingStart = header ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()]) : [[column.id, column.getSize()]];\n        const clientX = isTouchStartEvent(e) ? Math.round(e.touches[0].clientX) : e.clientX;\n        const newColumnSizing = {};\n        const updateOffset = (eventType, clientXPos) => {\n          if (typeof clientXPos !== 'number') {\n            return;\n          }\n          table.setColumnSizingInfo(old => {\n            var _old$startOffset, _old$startSize;\n            const deltaDirection = table.options.columnResizeDirection === 'rtl' ? -1 : 1;\n            const deltaOffset = (clientXPos - ((_old$startOffset = old == null ? void 0 : old.startOffset) != null ? _old$startOffset : 0)) * deltaDirection;\n            const deltaPercentage = Math.max(deltaOffset / ((_old$startSize = old == null ? void 0 : old.startSize) != null ? _old$startSize : 0), -0.999999);\n            old.columnSizingStart.forEach(_ref3 => {\n              let [columnId, headerSize] = _ref3;\n              newColumnSizing[columnId] = Math.round(Math.max(headerSize + headerSize * deltaPercentage, 0) * 100) / 100;\n            });\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage\n            };\n          });\n          if (table.options.columnResizeMode === 'onChange' || eventType === 'end') {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing\n            }));\n          }\n        };\n        const onMove = clientXPos => updateOffset('move', clientXPos);\n        const onEnd = clientXPos => {\n          updateOffset('end', clientXPos);\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: []\n          }));\n        };\n        const contextDocument = safelyAccessDocument(_contextDocument);\n        const mouseEvents = {\n          moveHandler: e => onMove(e.clientX),\n          upHandler: e => {\n            contextDocument == null || contextDocument.removeEventListener('mousemove', mouseEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('mouseup', mouseEvents.upHandler);\n            onEnd(e.clientX);\n          }\n        };\n        const touchEvents = {\n          moveHandler: e => {\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onMove(e.touches[0].clientX);\n            return false;\n          },\n          upHandler: e => {\n            var _e$touches$;\n            contextDocument == null || contextDocument.removeEventListener('touchmove', touchEvents.moveHandler);\n            contextDocument == null || contextDocument.removeEventListener('touchend', touchEvents.upHandler);\n            if (e.cancelable) {\n              e.preventDefault();\n              e.stopPropagation();\n            }\n            onEnd((_e$touches$ = e.touches[0]) == null ? void 0 : _e$touches$.clientX);\n          }\n        };\n        const passiveIfSupported = passiveEventSupported() ? {\n          passive: false\n        } : false;\n        if (isTouchStartEvent(e)) {\n          contextDocument == null || contextDocument.addEventListener('touchmove', touchEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('touchend', touchEvents.upHandler, passiveIfSupported);\n        } else {\n          contextDocument == null || contextDocument.addEventListener('mousemove', mouseEvents.moveHandler, passiveIfSupported);\n          contextDocument == null || contextDocument.addEventListener('mouseup', mouseEvents.upHandler, passiveIfSupported);\n        }\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id\n        }));\n      };\n    };\n  },\n  createTable: table => {\n    table.setColumnSizing = updater => table.options.onColumnSizingChange == null ? void 0 : table.options.onColumnSizingChange(updater);\n    table.setColumnSizingInfo = updater => table.options.onColumnSizingInfoChange == null ? void 0 : table.options.onColumnSizingInfoChange(updater);\n    table.resetColumnSizing = defaultState => {\n      var _table$initialState$c;\n      table.setColumnSizing(defaultState ? {} : (_table$initialState$c = table.initialState.columnSizing) != null ? _table$initialState$c : {});\n    };\n    table.resetHeaderSizeInfo = defaultState => {\n      var _table$initialState$c2;\n      table.setColumnSizingInfo(defaultState ? getDefaultColumnSizingInfoState() : (_table$initialState$c2 = table.initialState.columnSizingInfo) != null ? _table$initialState$c2 : getDefaultColumnSizingInfoState());\n    };\n    table.getTotalSize = () => {\n      var _table$getHeaderGroup, _table$getHeaderGroup2;\n      return (_table$getHeaderGroup = (_table$getHeaderGroup2 = table.getHeaderGroups()[0]) == null ? void 0 : _table$getHeaderGroup2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getHeaderGroup : 0;\n    };\n    table.getLeftTotalSize = () => {\n      var _table$getLeftHeaderG, _table$getLeftHeaderG2;\n      return (_table$getLeftHeaderG = (_table$getLeftHeaderG2 = table.getLeftHeaderGroups()[0]) == null ? void 0 : _table$getLeftHeaderG2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getLeftHeaderG : 0;\n    };\n    table.getCenterTotalSize = () => {\n      var _table$getCenterHeade, _table$getCenterHeade2;\n      return (_table$getCenterHeade = (_table$getCenterHeade2 = table.getCenterHeaderGroups()[0]) == null ? void 0 : _table$getCenterHeade2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getCenterHeade : 0;\n    };\n    table.getRightTotalSize = () => {\n      var _table$getRightHeader, _table$getRightHeader2;\n      return (_table$getRightHeader = (_table$getRightHeader2 = table.getRightHeaderGroups()[0]) == null ? void 0 : _table$getRightHeader2.headers.reduce((sum, header) => {\n        return sum + header.getSize();\n      }, 0)) != null ? _table$getRightHeader : 0;\n    };\n  }\n};\nlet passiveSupported = null;\nfunction passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported;\n  let supported = false;\n  try {\n    const options = {\n      get passive() {\n        supported = true;\n        return false;\n      }\n    };\n    const noop = () => {};\n    window.addEventListener('test', noop, options);\n    window.removeEventListener('test', noop);\n  } catch (err) {\n    supported = false;\n  }\n  passiveSupported = supported;\n  return passiveSupported;\n}\nfunction isTouchStartEvent(e) {\n  return e.type === 'touchstart';\n}\n\n//\n\nconst ColumnVisibility = {\n  getInitialState: state => {\n    return {\n      columnVisibility: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table)\n    };\n  },\n  createColumn: (column, table) => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value != null ? value : !column.getIsVisible()\n        }));\n      }\n    };\n    column.getIsVisible = () => {\n      var _ref, _table$getState$colum;\n      const childColumns = column.columns;\n      return (_ref = childColumns.length ? childColumns.some(c => c.getIsVisible()) : (_table$getState$colum = table.getState().columnVisibility) == null ? void 0 : _table$getState$colum[column.id]) != null ? _ref : true;\n    };\n    column.getCanHide = () => {\n      var _column$columnDef$ena, _table$options$enable;\n      return ((_column$columnDef$ena = column.columnDef.enableHiding) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableHiding) != null ? _table$options$enable : true);\n    };\n    column.getToggleVisibilityHandler = () => {\n      return e => {\n        column.toggleVisibility == null || column.toggleVisibility(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row._getAllVisibleCells = memo(() => [row.getAllCells(), table.getState().columnVisibility], cells => {\n      return cells.filter(cell => cell.column.getIsVisible());\n    }, getMemoOptions(table.options, 'debugRows', '_getAllVisibleCells'));\n    row.getVisibleCells = memo(() => [row.getLeftVisibleCells(), row.getCenterVisibleCells(), row.getRightVisibleCells()], (left, center, right) => [...left, ...center, ...right], getMemoOptions(table.options, 'debugRows', 'getVisibleCells'));\n  },\n  createTable: table => {\n    const makeVisibleColumnsMethod = (key, getColumns) => {\n      return memo(() => [getColumns(), getColumns().filter(d => d.getIsVisible()).map(d => d.id).join('_')], columns => {\n        return columns.filter(d => d.getIsVisible == null ? void 0 : d.getIsVisible());\n      }, getMemoOptions(table.options, 'debugColumns', key));\n    };\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod('getVisibleFlatColumns', () => table.getAllFlatColumns());\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod('getVisibleLeafColumns', () => table.getAllLeafColumns());\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod('getLeftVisibleLeafColumns', () => table.getLeftLeafColumns());\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod('getRightVisibleLeafColumns', () => table.getRightLeafColumns());\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod('getCenterVisibleLeafColumns', () => table.getCenterLeafColumns());\n    table.setColumnVisibility = updater => table.options.onColumnVisibilityChange == null ? void 0 : table.options.onColumnVisibilityChange(updater);\n    table.resetColumnVisibility = defaultState => {\n      var _table$initialState$c;\n      table.setColumnVisibility(defaultState ? {} : (_table$initialState$c = table.initialState.columnVisibility) != null ? _table$initialState$c : {});\n    };\n    table.toggleAllColumnsVisible = value => {\n      var _value;\n      value = (_value = value) != null ? _value : !table.getIsAllColumnsVisible();\n      table.setColumnVisibility(table.getAllLeafColumns().reduce((obj, column) => ({\n        ...obj,\n        [column.id]: !value ? !(column.getCanHide != null && column.getCanHide()) : value\n      }), {}));\n    };\n    table.getIsAllColumnsVisible = () => !table.getAllLeafColumns().some(column => !(column.getIsVisible != null && column.getIsVisible()));\n    table.getIsSomeColumnsVisible = () => table.getAllLeafColumns().some(column => column.getIsVisible == null ? void 0 : column.getIsVisible());\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return e => {\n        var _target;\n        table.toggleAllColumnsVisible((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nfunction _getVisibleLeafColumns(table, position) {\n  return !position ? table.getVisibleLeafColumns() : position === 'center' ? table.getCenterVisibleLeafColumns() : position === 'left' ? table.getLeftVisibleLeafColumns() : table.getRightVisibleLeafColumns();\n}\n\n//\n\nconst GlobalFaceting = {\n  createTable: table => {\n    table._getGlobalFacetedRowModel = table.options.getFacetedRowModel && table.options.getFacetedRowModel(table, '__global__');\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel();\n      }\n      return table._getGlobalFacetedRowModel();\n    };\n    table._getGlobalFacetedUniqueValues = table.options.getFacetedUniqueValues && table.options.getFacetedUniqueValues(table, '__global__');\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map();\n      }\n      return table._getGlobalFacetedUniqueValues();\n    };\n    table._getGlobalFacetedMinMaxValues = table.options.getFacetedMinMaxValues && table.options.getFacetedMinMaxValues(table, '__global__');\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return;\n      }\n      return table._getGlobalFacetedMinMaxValues();\n    };\n  }\n};\n\n//\n\nconst GlobalFiltering = {\n  getInitialState: state => {\n    return {\n      globalFilter: undefined,\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        var _table$getCoreRowMode;\n        const value = (_table$getCoreRowMode = table.getCoreRowModel().flatRows[0]) == null || (_table$getCoreRowMode = _table$getCoreRowMode._getAllCellsByColumnId()[column.id]) == null ? void 0 : _table$getCoreRowMode.getValue();\n        return typeof value === 'string' || typeof value === 'number';\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getCanGlobalFilter = () => {\n      var _column$columnDef$ena, _table$options$enable, _table$options$enable2, _table$options$getCol;\n      return ((_column$columnDef$ena = column.columnDef.enableGlobalFilter) != null ? _column$columnDef$ena : true) && ((_table$options$enable = table.options.enableGlobalFilter) != null ? _table$options$enable : true) && ((_table$options$enable2 = table.options.enableFilters) != null ? _table$options$enable2 : true) && ((_table$options$getCol = table.options.getColumnCanGlobalFilter == null ? void 0 : table.options.getColumnCanGlobalFilter(column)) != null ? _table$options$getCol : true) && !!column.accessorFn;\n    };\n  },\n  createTable: table => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString;\n    };\n    table.getGlobalFilterFn = () => {\n      var _table$options$filter, _table$options$filter2;\n      const {\n        globalFilterFn: globalFilterFn\n      } = table.options;\n      return isFunction(globalFilterFn) ? globalFilterFn : globalFilterFn === 'auto' ? table.getGlobalAutoFilterFn() : (_table$options$filter = (_table$options$filter2 = table.options.filterFns) == null ? void 0 : _table$options$filter2[globalFilterFn]) != null ? _table$options$filter : filterFns[globalFilterFn];\n    };\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange == null || table.options.onGlobalFilterChange(updater);\n    };\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(defaultState ? undefined : table.initialState.globalFilter);\n    };\n  }\n};\n\n//\n\nconst RowExpanding = {\n  getInitialState: state => {\n    return {\n      expanded: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetExpanded = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetExpanded) != null ? _ref : !table.options.manualExpanding) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetExpanded();\n          queued = false;\n        });\n      }\n    };\n    table.setExpanded = updater => table.options.onExpandedChange == null ? void 0 : table.options.onExpandedChange(updater);\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded != null ? expanded : !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true);\n      } else {\n        table.setExpanded({});\n      }\n    };\n    table.resetExpanded = defaultState => {\n      var _table$initialState$e, _table$initialState;\n      table.setExpanded(defaultState ? {} : (_table$initialState$e = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.expanded) != null ? _table$initialState$e : {});\n    };\n    table.getCanSomeRowsExpand = () => {\n      return table.getPrePaginationRowModel().flatRows.some(row => row.getCanExpand());\n    };\n    table.getToggleAllRowsExpandedHandler = () => {\n      return e => {\n        e.persist == null || e.persist();\n        table.toggleAllRowsExpanded();\n      };\n    };\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n      return expanded === true || Object.values(expanded).some(Boolean);\n    };\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded;\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true;\n      }\n      if (!Object.keys(expanded).length) {\n        return false;\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false;\n      }\n\n      // They must all be expanded :shrug:\n      return true;\n    };\n    table.getExpandedDepth = () => {\n      let maxDepth = 0;\n      const rowIds = table.getState().expanded === true ? Object.keys(table.getRowModel().rowsById) : Object.keys(table.getState().expanded);\n      rowIds.forEach(id => {\n        const splitId = id.split('.');\n        maxDepth = Math.max(maxDepth, splitId.length);\n      });\n      return maxDepth;\n    };\n    table.getPreExpandedRowModel = () => table.getSortedRowModel();\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table);\n      }\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel();\n      }\n      return table._getExpandedRowModel();\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        var _expanded;\n        const exists = old === true ? true : !!(old != null && old[row.id]);\n        let oldExpanded = {};\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true;\n          });\n        } else {\n          oldExpanded = old;\n        }\n        expanded = (_expanded = expanded) != null ? _expanded : !exists;\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true\n          };\n        }\n        if (exists && !expanded) {\n          const {\n            [row.id]: _,\n            ...rest\n          } = oldExpanded;\n          return rest;\n        }\n        return old;\n      });\n    };\n    row.getIsExpanded = () => {\n      var _table$options$getIsR;\n      const expanded = table.getState().expanded;\n      return !!((_table$options$getIsR = table.options.getIsRowExpanded == null ? void 0 : table.options.getIsRowExpanded(row)) != null ? _table$options$getIsR : expanded === true || (expanded == null ? void 0 : expanded[row.id]));\n    };\n    row.getCanExpand = () => {\n      var _table$options$getRow, _table$options$enable, _row$subRows;\n      return (_table$options$getRow = table.options.getRowCanExpand == null ? void 0 : table.options.getRowCanExpand(row)) != null ? _table$options$getRow : ((_table$options$enable = table.options.enableExpanding) != null ? _table$options$enable : true) && !!((_row$subRows = row.subRows) != null && _row$subRows.length);\n    };\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true;\n      let currentRow = row;\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true);\n        isFullyExpanded = currentRow.getIsExpanded();\n      }\n      return isFullyExpanded;\n    };\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand();\n      return () => {\n        if (!canExpand) return;\n        row.toggleExpanded();\n      };\n    };\n  }\n};\n\n//\n\nconst defaultPageIndex = 0;\nconst defaultPageSize = 10;\nconst getDefaultPaginationState = () => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize\n});\nconst RowPagination = {\n  getInitialState: state => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...(state == null ? void 0 : state.pagination)\n      }\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table)\n    };\n  },\n  createTable: table => {\n    let registered = false;\n    let queued = false;\n    table._autoResetPageIndex = () => {\n      var _ref, _table$options$autoRe;\n      if (!registered) {\n        table._queue(() => {\n          registered = true;\n        });\n        return;\n      }\n      if ((_ref = (_table$options$autoRe = table.options.autoResetAll) != null ? _table$options$autoRe : table.options.autoResetPageIndex) != null ? _ref : !table.options.manualPagination) {\n        if (queued) return;\n        queued = true;\n        table._queue(() => {\n          table.resetPageIndex();\n          queued = false;\n        });\n      }\n    };\n    table.setPagination = updater => {\n      const safeUpdater = old => {\n        let newState = functionalUpdate(updater, old);\n        return newState;\n      };\n      return table.options.onPaginationChange == null ? void 0 : table.options.onPaginationChange(safeUpdater);\n    };\n    table.resetPagination = defaultState => {\n      var _table$initialState$p;\n      table.setPagination(defaultState ? getDefaultPaginationState() : (_table$initialState$p = table.initialState.pagination) != null ? _table$initialState$p : getDefaultPaginationState());\n    };\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex);\n        const maxPageIndex = typeof table.options.pageCount === 'undefined' || table.options.pageCount === -1 ? Number.MAX_SAFE_INTEGER : table.options.pageCount - 1;\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex));\n        return {\n          ...old,\n          pageIndex\n        };\n      });\n    };\n    table.resetPageIndex = defaultState => {\n      var _table$initialState$p2, _table$initialState;\n      table.setPageIndex(defaultState ? defaultPageIndex : (_table$initialState$p2 = (_table$initialState = table.initialState) == null || (_table$initialState = _table$initialState.pagination) == null ? void 0 : _table$initialState.pageIndex) != null ? _table$initialState$p2 : defaultPageIndex);\n    };\n    table.resetPageSize = defaultState => {\n      var _table$initialState$p3, _table$initialState2;\n      table.setPageSize(defaultState ? defaultPageSize : (_table$initialState$p3 = (_table$initialState2 = table.initialState) == null || (_table$initialState2 = _table$initialState2.pagination) == null ? void 0 : _table$initialState2.pageSize) != null ? _table$initialState$p3 : defaultPageSize);\n    };\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize));\n        const topRowIndex = old.pageSize * old.pageIndex;\n        const pageIndex = Math.floor(topRowIndex / pageSize);\n        return {\n          ...old,\n          pageIndex,\n          pageSize\n        };\n      });\n    };\n    //deprecated\n    table.setPageCount = updater => table.setPagination(old => {\n      var _table$options$pageCo;\n      let newPageCount = functionalUpdate(updater, (_table$options$pageCo = table.options.pageCount) != null ? _table$options$pageCo : -1);\n      if (typeof newPageCount === 'number') {\n        newPageCount = Math.max(-1, newPageCount);\n      }\n      return {\n        ...old,\n        pageCount: newPageCount\n      };\n    });\n    table.getPageOptions = memo(() => [table.getPageCount()], pageCount => {\n      let pageOptions = [];\n      if (pageCount && pageCount > 0) {\n        pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i);\n      }\n      return pageOptions;\n    }, getMemoOptions(table.options, 'debugTable', 'getPageOptions'));\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0;\n    table.getCanNextPage = () => {\n      const {\n        pageIndex\n      } = table.getState().pagination;\n      const pageCount = table.getPageCount();\n      if (pageCount === -1) {\n        return true;\n      }\n      if (pageCount === 0) {\n        return false;\n      }\n      return pageIndex < pageCount - 1;\n    };\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1);\n    };\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1;\n      });\n    };\n    table.firstPage = () => {\n      return table.setPageIndex(0);\n    };\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1);\n    };\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel();\n    table.getPaginationRowModel = () => {\n      if (!table._getPaginationRowModel && table.options.getPaginationRowModel) {\n        table._getPaginationRowModel = table.options.getPaginationRowModel(table);\n      }\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel();\n      }\n      return table._getPaginationRowModel();\n    };\n    table.getPageCount = () => {\n      var _table$options$pageCo2;\n      return (_table$options$pageCo2 = table.options.pageCount) != null ? _table$options$pageCo2 : Math.ceil(table.getRowCount() / table.getState().pagination.pageSize);\n    };\n    table.getRowCount = () => {\n      var _table$options$rowCou;\n      return (_table$options$rowCou = table.options.rowCount) != null ? _table$options$rowCou : table.getPrePaginationRowModel().rows.length;\n    };\n  }\n};\n\n//\n\nconst getDefaultRowPinningState = () => ({\n  top: [],\n  bottom: []\n});\nconst RowPinning = {\n  getInitialState: state => {\n    return {\n      rowPinning: getDefaultRowPinningState(),\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowPinningChange: makeStateUpdater('rowPinning', table)\n    };\n  },\n  createRow: (row, table) => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows ? row.getLeafRows().map(_ref => {\n        let {\n          id\n        } = _ref;\n        return id;\n      }) : [];\n      const parentRowIds = includeParentRows ? row.getParentRows().map(_ref2 => {\n        let {\n          id\n        } = _ref2;\n        return id;\n      }) : [];\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds]);\n      table.setRowPinning(old => {\n        var _old$top3, _old$bottom3;\n        if (position === 'bottom') {\n          var _old$top, _old$bottom;\n          return {\n            top: ((_old$top = old == null ? void 0 : old.top) != null ? _old$top : []).filter(d => !(rowIds != null && rowIds.has(d))),\n            bottom: [...((_old$bottom = old == null ? void 0 : old.bottom) != null ? _old$bottom : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)]\n          };\n        }\n        if (position === 'top') {\n          var _old$top2, _old$bottom2;\n          return {\n            top: [...((_old$top2 = old == null ? void 0 : old.top) != null ? _old$top2 : []).filter(d => !(rowIds != null && rowIds.has(d))), ...Array.from(rowIds)],\n            bottom: ((_old$bottom2 = old == null ? void 0 : old.bottom) != null ? _old$bottom2 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n          };\n        }\n        return {\n          top: ((_old$top3 = old == null ? void 0 : old.top) != null ? _old$top3 : []).filter(d => !(rowIds != null && rowIds.has(d))),\n          bottom: ((_old$bottom3 = old == null ? void 0 : old.bottom) != null ? _old$bottom3 : []).filter(d => !(rowIds != null && rowIds.has(d)))\n        };\n      });\n    };\n    row.getCanPin = () => {\n      var _ref3;\n      const {\n        enableRowPinning,\n        enablePinning\n      } = table.options;\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row);\n      }\n      return (_ref3 = enableRowPinning != null ? enableRowPinning : enablePinning) != null ? _ref3 : true;\n    };\n    row.getIsPinned = () => {\n      const rowIds = [row.id];\n      const {\n        top,\n        bottom\n      } = table.getState().rowPinning;\n      const isTop = rowIds.some(d => top == null ? void 0 : top.includes(d));\n      const isBottom = rowIds.some(d => bottom == null ? void 0 : bottom.includes(d));\n      return isTop ? 'top' : isBottom ? 'bottom' : false;\n    };\n    row.getPinnedIndex = () => {\n      var _ref4, _visiblePinnedRowIds$;\n      const position = row.getIsPinned();\n      if (!position) return -1;\n      const visiblePinnedRowIds = (_ref4 = position === 'top' ? table.getTopRows() : table.getBottomRows()) == null ? void 0 : _ref4.map(_ref5 => {\n        let {\n          id\n        } = _ref5;\n        return id;\n      });\n      return (_visiblePinnedRowIds$ = visiblePinnedRowIds == null ? void 0 : visiblePinnedRowIds.indexOf(row.id)) != null ? _visiblePinnedRowIds$ : -1;\n    };\n  },\n  createTable: table => {\n    table.setRowPinning = updater => table.options.onRowPinningChange == null ? void 0 : table.options.onRowPinningChange(updater);\n    table.resetRowPinning = defaultState => {\n      var _table$initialState$r, _table$initialState;\n      return table.setRowPinning(defaultState ? getDefaultRowPinningState() : (_table$initialState$r = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.rowPinning) != null ? _table$initialState$r : getDefaultRowPinningState());\n    };\n    table.getIsSomeRowsPinned = position => {\n      var _pinningState$positio;\n      const pinningState = table.getState().rowPinning;\n      if (!position) {\n        var _pinningState$top, _pinningState$bottom;\n        return Boolean(((_pinningState$top = pinningState.top) == null ? void 0 : _pinningState$top.length) || ((_pinningState$bottom = pinningState.bottom) == null ? void 0 : _pinningState$bottom.length));\n      }\n      return Boolean((_pinningState$positio = pinningState[position]) == null ? void 0 : _pinningState$positio.length);\n    };\n    table._getPinnedRows = (visibleRows, pinnedRowIds, position) => {\n      var _table$options$keepPi;\n      const rows = ((_table$options$keepPi = table.options.keepPinnedRows) != null ? _table$options$keepPi : true) ?\n      //get all rows that are pinned even if they would not be otherwise visible\n      //account for expanded parent rows, but not pagination or filtering\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => {\n        const row = table.getRow(rowId, true);\n        return row.getIsAllParentsExpanded() ? row : null;\n      }) :\n      //else get only visible rows that are pinned\n      (pinnedRowIds != null ? pinnedRowIds : []).map(rowId => visibleRows.find(row => row.id === rowId));\n      return rows.filter(Boolean).map(d => ({\n        ...d,\n        position\n      }));\n    };\n    table.getTopRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top], (allRows, topPinnedRowIds) => table._getPinnedRows(allRows, topPinnedRowIds, 'top'), getMemoOptions(table.options, 'debugRows', 'getTopRows'));\n    table.getBottomRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.bottom], (allRows, bottomPinnedRowIds) => table._getPinnedRows(allRows, bottomPinnedRowIds, 'bottom'), getMemoOptions(table.options, 'debugRows', 'getBottomRows'));\n    table.getCenterRows = memo(() => [table.getRowModel().rows, table.getState().rowPinning.top, table.getState().rowPinning.bottom], (allRows, top, bottom) => {\n      const topAndBottom = new Set([...(top != null ? top : []), ...(bottom != null ? bottom : [])]);\n      return allRows.filter(d => !topAndBottom.has(d.id));\n    }, getMemoOptions(table.options, 'debugRows', 'getCenterRows'));\n  }\n};\n\n//\n\nconst RowSelection = {\n  getInitialState: state => {\n    return {\n      rowSelection: {},\n      ...state\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    };\n  },\n  createTable: table => {\n    table.setRowSelection = updater => table.options.onRowSelectionChange == null ? void 0 : table.options.onRowSelectionChange(updater);\n    table.resetRowSelection = defaultState => {\n      var _table$initialState$r;\n      return table.setRowSelection(defaultState ? {} : (_table$initialState$r = table.initialState.rowSelection) != null ? _table$initialState$r : {});\n    };\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected();\n        const rowSelection = {\n          ...old\n        };\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows;\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return;\n            }\n            rowSelection[row.id] = true;\n          });\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id];\n          });\n        }\n        return rowSelection;\n      });\n    };\n    table.toggleAllPageRowsSelected = value => table.setRowSelection(old => {\n      const resolvedValue = typeof value !== 'undefined' ? value : !table.getIsAllPageRowsSelected();\n      const rowSelection = {\n        ...old\n      };\n      table.getRowModel().rows.forEach(row => {\n        mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table);\n      });\n      return rowSelection;\n    });\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel();\n    table.getSelectedRowModel = memo(() => [table.getState().rowSelection, table.getCoreRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel'));\n    table.getFilteredSelectedRowModel = memo(() => [table.getState().rowSelection, table.getFilteredRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel'));\n    table.getGroupedSelectedRowModel = memo(() => [table.getState().rowSelection, table.getSortedRowModel()], (rowSelection, rowModel) => {\n      if (!Object.keys(rowSelection).length) {\n        return {\n          rows: [],\n          flatRows: [],\n          rowsById: {}\n        };\n      }\n      return selectRowsFn(table, rowModel);\n    }, getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel'));\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows;\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllRowsSelected = Boolean(preGroupedFlatRows.length && Object.keys(rowSelection).length);\n      if (isAllRowsSelected) {\n        if (preGroupedFlatRows.some(row => row.getCanSelect() && !rowSelection[row.id])) {\n          isAllRowsSelected = false;\n        }\n      }\n      return isAllRowsSelected;\n    };\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows.filter(row => row.getCanSelect());\n      const {\n        rowSelection\n      } = table.getState();\n      let isAllPageRowsSelected = !!paginationFlatRows.length;\n      if (isAllPageRowsSelected && paginationFlatRows.some(row => !rowSelection[row.id])) {\n        isAllPageRowsSelected = false;\n      }\n      return isAllPageRowsSelected;\n    };\n    table.getIsSomeRowsSelected = () => {\n      var _table$getState$rowSe;\n      const totalSelected = Object.keys((_table$getState$rowSe = table.getState().rowSelection) != null ? _table$getState$rowSe : {}).length;\n      return totalSelected > 0 && totalSelected < table.getFilteredRowModel().flatRows.length;\n    };\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows;\n      return table.getIsAllPageRowsSelected() ? false : paginationFlatRows.filter(row => row.getCanSelect()).some(d => d.getIsSelected() || d.getIsSomeSelected());\n    };\n    table.getToggleAllRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllRowsSelected(e.target.checked);\n      };\n    };\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return e => {\n        table.toggleAllPageRowsSelected(e.target.checked);\n      };\n    };\n  },\n  createRow: (row, table) => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected();\n      table.setRowSelection(old => {\n        var _opts$selectChildren;\n        value = typeof value !== 'undefined' ? value : !isSelected;\n        if (row.getCanSelect() && isSelected === value) {\n          return old;\n        }\n        const selectedRowIds = {\n          ...old\n        };\n        mutateRowIsSelected(selectedRowIds, row.id, value, (_opts$selectChildren = opts == null ? void 0 : opts.selectChildren) != null ? _opts$selectChildren : true, table);\n        return selectedRowIds;\n      });\n    };\n    row.getIsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isRowSelected(row, rowSelection);\n    };\n    row.getIsSomeSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'some';\n    };\n    row.getIsAllSubRowsSelected = () => {\n      const {\n        rowSelection\n      } = table.getState();\n      return isSubRowSelected(row, rowSelection) === 'all';\n    };\n    row.getCanSelect = () => {\n      var _table$options$enable;\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row);\n      }\n      return (_table$options$enable = table.options.enableRowSelection) != null ? _table$options$enable : true;\n    };\n    row.getCanSelectSubRows = () => {\n      var _table$options$enable2;\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row);\n      }\n      return (_table$options$enable2 = table.options.enableSubRowSelection) != null ? _table$options$enable2 : true;\n    };\n    row.getCanMultiSelect = () => {\n      var _table$options$enable3;\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row);\n      }\n      return (_table$options$enable3 = table.options.enableMultiRowSelection) != null ? _table$options$enable3 : true;\n    };\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect();\n      return e => {\n        var _target;\n        if (!canSelect) return;\n        row.toggleSelected((_target = e.target) == null ? void 0 : _target.checked);\n      };\n    };\n  }\n};\nconst mutateRowIsSelected = (selectedRowIds, id, value, includeChildren, table) => {\n  var _row$subRows;\n  const row = table.getRow(id, true);\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key]);\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true;\n    }\n  } else {\n    delete selectedRowIds[id];\n  }\n  // }\n\n  if (includeChildren && (_row$subRows = row.subRows) != null && _row$subRows.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row => mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table));\n  }\n};\nfunction selectRowsFn(table, rowModel) {\n  const rowSelection = table.getState().rowSelection;\n  const newSelectedFlatRows = [];\n  const newSelectedRowsById = {};\n\n  // Filters top level and nested rows\n  const recurseRows = function (rows, depth) {\n    return rows.map(row => {\n      var _row$subRows2;\n      const isSelected = isRowSelected(row, rowSelection);\n      if (isSelected) {\n        newSelectedFlatRows.push(row);\n        newSelectedRowsById[row.id] = row;\n      }\n      if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length) {\n        row = {\n          ...row,\n          subRows: recurseRows(row.subRows)\n        };\n      }\n      if (isSelected) {\n        return row;\n      }\n    }).filter(Boolean);\n  };\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById\n  };\n}\nfunction isRowSelected(row, selection) {\n  var _selection$row$id;\n  return (_selection$row$id = selection[row.id]) != null ? _selection$row$id : false;\n}\nfunction isSubRowSelected(row, selection, table) {\n  var _row$subRows3;\n  if (!((_row$subRows3 = row.subRows) != null && _row$subRows3.length)) return false;\n  let allChildrenSelected = true;\n  let someSelected = false;\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return;\n    }\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection);\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true;\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true;\n        allChildrenSelected = false;\n      } else {\n        allChildrenSelected = false;\n      }\n    }\n  });\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false;\n}\n\nconst reSplitAlphaNumeric = /([0-9]+)/gm;\nconst alphanumeric = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\nconst alphanumericCaseSensitive = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)).toLowerCase(), toString(rowB.getValue(columnId)).toLowerCase());\n};\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive = (rowA, rowB, columnId) => {\n  return compareBasic(toString(rowA.getValue(columnId)), toString(rowB.getValue(columnId)));\n};\nconst datetime = (rowA, rowB, columnId) => {\n  const a = rowA.getValue(columnId);\n  const b = rowB.getValue(columnId);\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0;\n};\nconst basic = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId));\n};\n\n// Utils\n\nfunction compareBasic(a, b) {\n  return a === b ? 0 : a > b ? 1 : -1;\n}\nfunction toString(a) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return '';\n    }\n    return String(a);\n  }\n  if (typeof a === 'string') {\n    return a;\n  }\n  return '';\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr, bStr) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean);\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean);\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift();\n    const bb = b.shift();\n    const an = parseInt(aa, 10);\n    const bn = parseInt(bb, 10);\n    const combo = [an, bn].sort();\n\n    // Both are string\n    if (isNaN(combo[0])) {\n      if (aa > bb) {\n        return 1;\n      }\n      if (bb > aa) {\n        return -1;\n      }\n      continue;\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1])) {\n      return isNaN(an) ? -1 : 1;\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1;\n    }\n    if (bn > an) {\n      return -1;\n    }\n  }\n  return a.length - b.length;\n}\n\n// Exports\n\nconst sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic\n};\n\n//\n\nconst RowSorting = {\n  getInitialState: state => {\n    return {\n      sorting: [],\n      ...state\n    };\n  },\n  getDefaultColumnDef: () => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1\n    };\n  },\n  getDefaultOptions: table => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: e => {\n        return e.shiftKey;\n      }\n    };\n  },\n  createColumn: (column, table) => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10);\n      let isString = false;\n      for (const row of firstRows) {\n        const value = row == null ? void 0 : row.getValue(column.id);\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime;\n        }\n        if (typeof value === 'string') {\n          isString = true;\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric;\n          }\n        }\n      }\n      if (isString) {\n        return sortingFns.text;\n      }\n      return sortingFns.basic;\n    };\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0];\n      const value = firstRow == null ? void 0 : firstRow.getValue(column.id);\n      if (typeof value === 'string') {\n        return 'asc';\n      }\n      return 'desc';\n    };\n    column.getSortingFn = () => {\n      var _table$options$sortin, _table$options$sortin2;\n      if (!column) {\n        throw new Error();\n      }\n      return isFunction(column.columnDef.sortingFn) ? column.columnDef.sortingFn : column.columnDef.sortingFn === 'auto' ? column.getAutoSortingFn() : (_table$options$sortin = (_table$options$sortin2 = table.options.sortingFns) == null ? void 0 : _table$options$sortin2[column.columnDef.sortingFn]) != null ? _table$options$sortin : sortingFns[column.columnDef.sortingFn];\n    };\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder();\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null;\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old == null ? void 0 : old.find(d => d.id === column.id);\n        const existingIndex = old == null ? void 0 : old.findIndex(d => d.id === column.id);\n        let newSorting = [];\n\n        // What should we do with this sort action?\n        let sortAction;\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc';\n\n        // Multi-mode\n        if (old != null && old.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'add';\n          }\n        } else {\n          // Normal mode\n          if (old != null && old.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace';\n          } else if (existingSorting) {\n            sortAction = 'toggle';\n          } else {\n            sortAction = 'replace';\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove';\n            }\n          }\n        }\n        if (sortAction === 'add') {\n          var _table$options$maxMul;\n          newSorting = [...old, {\n            id: column.id,\n            desc: nextDesc\n          }];\n          // Take latest n columns\n          newSorting.splice(0, newSorting.length - ((_table$options$maxMul = table.options.maxMultiSortColCount) != null ? _table$options$maxMul : Number.MAX_SAFE_INTEGER));\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc\n              };\n            }\n            return d;\n          });\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id);\n        } else {\n          newSorting = [{\n            id: column.id,\n            desc: nextDesc\n          }];\n        }\n        return newSorting;\n      });\n    };\n    column.getFirstSortDir = () => {\n      var _ref, _column$columnDef$sor;\n      const sortDescFirst = (_ref = (_column$columnDef$sor = column.columnDef.sortDescFirst) != null ? _column$columnDef$sor : table.options.sortDescFirst) != null ? _ref : column.getAutoSortDir() === 'desc';\n      return sortDescFirst ? 'desc' : 'asc';\n    };\n    column.getNextSortingOrder = multi => {\n      var _table$options$enable, _table$options$enable2;\n      const firstSortDirection = column.getFirstSortDir();\n      const isSorted = column.getIsSorted();\n      if (!isSorted) {\n        return firstSortDirection;\n      }\n      if (isSorted !== firstSortDirection && ((_table$options$enable = table.options.enableSortingRemoval) != null ? _table$options$enable : true) && (\n      // If enableSortRemove, enable in general\n      multi ? (_table$options$enable2 = table.options.enableMultiRemove) != null ? _table$options$enable2 : true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false;\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc';\n    };\n    column.getCanSort = () => {\n      var _column$columnDef$ena, _table$options$enable3;\n      return ((_column$columnDef$ena = column.columnDef.enableSorting) != null ? _column$columnDef$ena : true) && ((_table$options$enable3 = table.options.enableSorting) != null ? _table$options$enable3 : true) && !!column.accessorFn;\n    };\n    column.getCanMultiSort = () => {\n      var _ref2, _column$columnDef$ena2;\n      return (_ref2 = (_column$columnDef$ena2 = column.columnDef.enableMultiSort) != null ? _column$columnDef$ena2 : table.options.enableMultiSort) != null ? _ref2 : !!column.accessorFn;\n    };\n    column.getIsSorted = () => {\n      var _table$getState$sorti;\n      const columnSort = (_table$getState$sorti = table.getState().sorting) == null ? void 0 : _table$getState$sorti.find(d => d.id === column.id);\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc';\n    };\n    column.getSortIndex = () => {\n      var _table$getState$sorti2, _table$getState$sorti3;\n      return (_table$getState$sorti2 = (_table$getState$sorti3 = table.getState().sorting) == null ? void 0 : _table$getState$sorti3.findIndex(d => d.id === column.id)) != null ? _table$getState$sorti2 : -1;\n    };\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old => old != null && old.length ? old.filter(d => d.id !== column.id) : []);\n    };\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort();\n      return e => {\n        if (!canSort) return;\n        e.persist == null || e.persist();\n        column.toggleSorting == null || column.toggleSorting(undefined, column.getCanMultiSort() ? table.options.isMultiSortEvent == null ? void 0 : table.options.isMultiSortEvent(e) : false);\n      };\n    };\n  },\n  createTable: table => {\n    table.setSorting = updater => table.options.onSortingChange == null ? void 0 : table.options.onSortingChange(updater);\n    table.resetSorting = defaultState => {\n      var _table$initialState$s, _table$initialState;\n      table.setSorting(defaultState ? [] : (_table$initialState$s = (_table$initialState = table.initialState) == null ? void 0 : _table$initialState.sorting) != null ? _table$initialState$s : []);\n    };\n    table.getPreSortedRowModel = () => table.getGroupedRowModel();\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table);\n      }\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel();\n      }\n      return table._getSortedRowModel();\n    };\n  }\n};\n\nconst builtInFeatures = [Headers, ColumnVisibility, ColumnOrdering, ColumnPinning, ColumnFaceting, ColumnFiltering, GlobalFaceting,\n//depends on ColumnFaceting\nGlobalFiltering,\n//depends on ColumnFiltering\nRowSorting, ColumnGrouping,\n//depends on RowSorting\nRowExpanding, RowPagination, RowPinning, RowSelection, ColumnSizing];\n\n//\n\nfunction createTable(options) {\n  var _options$_features, _options$initialState;\n  if ( true && (options.debugAll || options.debugTable)) {\n    console.info('Creating Table Instance...');\n  }\n  const _features = [...builtInFeatures, ...((_options$_features = options._features) != null ? _options$_features : [])];\n  let table = {\n    _features\n  };\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions == null ? void 0 : feature.getDefaultOptions(table));\n  }, {});\n  const mergeOptions = options => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options);\n    }\n    return {\n      ...defaultOptions,\n      ...options\n    };\n  };\n  const coreInitialState = {};\n  let initialState = {\n    ...coreInitialState,\n    ...((_options$initialState = options.initialState) != null ? _options$initialState : {})\n  };\n  table._features.forEach(feature => {\n    var _feature$getInitialSt;\n    initialState = (_feature$getInitialSt = feature.getInitialState == null ? void 0 : feature.getInitialState(initialState)) != null ? _feature$getInitialSt : initialState;\n  });\n  const queued = [];\n  let queuedTimeout = false;\n  const coreInstance = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb);\n      if (!queuedTimeout) {\n        queuedTimeout = true;\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve().then(() => {\n          while (queued.length) {\n            queued.shift()();\n          }\n          queuedTimeout = false;\n        }).catch(error => setTimeout(() => {\n          throw error;\n        }));\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState);\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options);\n      table.options = mergeOptions(newOptions);\n    },\n    getState: () => {\n      return table.options.state;\n    },\n    setState: updater => {\n      table.options.onStateChange == null || table.options.onStateChange(updater);\n    },\n    _getRowId: (row, index, parent) => {\n      var _table$options$getRow;\n      return (_table$options$getRow = table.options.getRowId == null ? void 0 : table.options.getRowId(row, index, parent)) != null ? _table$options$getRow : `${parent ? [parent.id, index].join('.') : index}`;\n    },\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table);\n      }\n      return table._getCoreRowModel();\n    },\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel();\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id, searchAll) => {\n      let row = (searchAll ? table.getPrePaginationRowModel() : table.getRowModel()).rowsById[id];\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id];\n        if (!row) {\n          if (true) {\n            throw new Error(`getRow could not find row with ID: ${id}`);\n          }\n          throw new Error();\n        }\n      }\n      return row;\n    },\n    _getDefaultColumnDef: memo(() => [table.options.defaultColumn], defaultColumn => {\n      var _defaultColumn;\n      defaultColumn = (_defaultColumn = defaultColumn) != null ? _defaultColumn : {};\n      return {\n        header: props => {\n          const resolvedColumnDef = props.header.column.columnDef;\n          if (resolvedColumnDef.accessorKey) {\n            return resolvedColumnDef.accessorKey;\n          }\n          if (resolvedColumnDef.accessorFn) {\n            return resolvedColumnDef.id;\n          }\n          return null;\n        },\n        // footer: props => props.header.column.id,\n        cell: props => {\n          var _props$renderValue$to, _props$renderValue;\n          return (_props$renderValue$to = (_props$renderValue = props.renderValue()) == null || _props$renderValue.toString == null ? void 0 : _props$renderValue.toString()) != null ? _props$renderValue$to : null;\n        },\n        ...table._features.reduce((obj, feature) => {\n          return Object.assign(obj, feature.getDefaultColumnDef == null ? void 0 : feature.getDefaultColumnDef());\n        }, {}),\n        ...defaultColumn\n      };\n    }, getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')),\n    _getColumnDefs: () => table.options.columns,\n    getAllColumns: memo(() => [table._getColumnDefs()], columnDefs => {\n      const recurseColumns = function (columnDefs, parent, depth) {\n        if (depth === void 0) {\n          depth = 0;\n        }\n        return columnDefs.map(columnDef => {\n          const column = createColumn(table, columnDef, depth, parent);\n          const groupingColumnDef = columnDef;\n          column.columns = groupingColumnDef.columns ? recurseColumns(groupingColumnDef.columns, column, depth + 1) : [];\n          return column;\n        });\n      };\n      return recurseColumns(columnDefs);\n    }, getMemoOptions(options, 'debugColumns', 'getAllColumns')),\n    getAllFlatColumns: memo(() => [table.getAllColumns()], allColumns => {\n      return allColumns.flatMap(column => {\n        return column.getFlatColumns();\n      });\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')),\n    _getAllFlatColumnsById: memo(() => [table.getAllFlatColumns()], flatColumns => {\n      return flatColumns.reduce((acc, column) => {\n        acc[column.id] = column;\n        return acc;\n      }, {});\n    }, getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')),\n    getAllLeafColumns: memo(() => [table.getAllColumns(), table._getOrderColumnsFn()], (allColumns, orderColumns) => {\n      let leafColumns = allColumns.flatMap(column => column.getLeafColumns());\n      return orderColumns(leafColumns);\n    }, getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')),\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId];\n      if ( true && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`);\n      }\n      return column;\n    }\n  };\n  Object.assign(table, coreInstance);\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index];\n    feature == null || feature.createTable == null || feature.createTable(table);\n  }\n  return table;\n}\n\nfunction getCoreRowModel() {\n  return table => memo(() => [table.options.data], data => {\n    const rowModel = {\n      rows: [],\n      flatRows: [],\n      rowsById: {}\n    };\n    const accessRows = function (originalRows, depth, parentRow) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      const rows = [];\n      for (let i = 0; i < originalRows.length; i++) {\n        // This could be an expensive check at scale, so we should move it somewhere else, but where?\n        // if (!id) {\n        //   if (process.env.NODE_ENV !== 'production') {\n        //     throw new Error(`getRowId expected an ID, but got ${id}`)\n        //   }\n        // }\n\n        // Make the row\n        const row = createRow(table, table._getRowId(originalRows[i], i, parentRow), originalRows[i], i, depth, undefined, parentRow == null ? void 0 : parentRow.id);\n\n        // Keep track of every row in a flat array\n        rowModel.flatRows.push(row);\n        // Also keep track of every row by its ID\n        rowModel.rowsById[row.id] = row;\n        // Push table row into parent\n        rows.push(row);\n\n        // Get the original subrows\n        if (table.options.getSubRows) {\n          var _row$originalSubRows;\n          row.originalSubRows = table.options.getSubRows(originalRows[i], i);\n\n          // Then recursively access them\n          if ((_row$originalSubRows = row.originalSubRows) != null && _row$originalSubRows.length) {\n            row.subRows = accessRows(row.originalSubRows, depth + 1, row);\n          }\n        }\n      }\n      return rows;\n    };\n    rowModel.rows = accessRows(data);\n    return rowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getExpandedRowModel() {\n  return table => memo(() => [table.getState().expanded, table.getPreExpandedRowModel(), table.options.paginateExpandedRows], (expanded, rowModel, paginateExpandedRows) => {\n    if (!rowModel.rows.length || expanded !== true && !Object.keys(expanded != null ? expanded : {}).length) {\n      return rowModel;\n    }\n    if (!paginateExpandedRows) {\n      // Only expand rows at this point if they are being paginated\n      return rowModel;\n    }\n    return expandRows(rowModel);\n  }, getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel'));\n}\nfunction expandRows(rowModel) {\n  const expandedRows = [];\n  const handleRow = row => {\n    var _row$subRows;\n    expandedRows.push(row);\n    if ((_row$subRows = row.subRows) != null && _row$subRows.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow);\n    }\n  };\n  rowModel.rows.forEach(handleRow);\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById\n  };\n}\n\nfunction getFacetedMinMaxValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return undefined;\n    const uniqueValues = facetedRowModel.flatRows.flatMap(flatRow => {\n      var _flatRow$getUniqueVal;\n      return (_flatRow$getUniqueVal = flatRow.getUniqueValues(columnId)) != null ? _flatRow$getUniqueVal : [];\n    }).map(Number).filter(value => !Number.isNaN(value));\n    if (!uniqueValues.length) return;\n    let facetedMinValue = uniqueValues[0];\n    let facetedMaxValue = uniqueValues[uniqueValues.length - 1];\n    for (const value of uniqueValues) {\n      if (value < facetedMinValue) facetedMinValue = value;else if (value > facetedMaxValue) facetedMaxValue = value;\n    }\n    return [facetedMinValue, facetedMaxValue];\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues'));\n}\n\nfunction filterRows(rows, filterRowImpl, table) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table);\n  }\n  return filterRowModelFromRoot(rows, filterRowImpl, table);\n}\nfunction filterRowModelFromLeafs(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea : 100;\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    const rows = [];\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      var _row$subRows;\n      let row = rowsToFilter[i];\n      const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n      newRow.columnFilters = row.columnFilters;\n      if ((_row$subRows = row.subRows) != null && _row$subRows.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n        row = newRow;\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n          continue;\n        }\n      } else {\n        row = newRow;\n        if (filterRow(row)) {\n          rows.push(row);\n          newFilteredRowsById[row.id] = row;\n          newFilteredFlatRows.push(row);\n        }\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\nfunction filterRowModelFromRoot(rowsToFilter, filterRow, table) {\n  var _table$options$maxLea2;\n  const newFilteredFlatRows = [];\n  const newFilteredRowsById = {};\n  const maxDepth = (_table$options$maxLea2 = table.options.maxLeafRowFilterDepth) != null ? _table$options$maxLea2 : 100;\n\n  // Filters top level and nested rows\n  const recurseFilterRows = function (rowsToFilter, depth) {\n    if (depth === void 0) {\n      depth = 0;\n    }\n    // Filter from parents downward first\n\n    const rows = [];\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i];\n      const pass = filterRow(row);\n      if (pass) {\n        var _row$subRows2;\n        if ((_row$subRows2 = row.subRows) != null && _row$subRows2.length && depth < maxDepth) {\n          const newRow = createRow(table, row.id, row.original, row.index, row.depth, undefined, row.parentId);\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1);\n          row = newRow;\n        }\n        rows.push(row);\n        newFilteredFlatRows.push(row);\n        newFilteredRowsById[row.id] = row;\n      }\n    }\n    return rows;\n  };\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById\n  };\n}\n\nfunction getFacetedRowModel() {\n  return (table, columnId) => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter, table.getFilteredRowModel()], (preRowModel, columnFilters, globalFilter) => {\n    if (!preRowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      return preRowModel;\n    }\n    const filterableIds = [...columnFilters.map(d => d.id).filter(d => d !== columnId), globalFilter ? '__global__' : undefined].filter(Boolean);\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n    return filterRows(preRowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel'));\n}\n\nfunction getFacetedUniqueValues() {\n  return (table, columnId) => memo(() => {\n    var _table$getColumn;\n    return [(_table$getColumn = table.getColumn(columnId)) == null ? void 0 : _table$getColumn.getFacetedRowModel()];\n  }, facetedRowModel => {\n    if (!facetedRowModel) return new Map();\n    let facetedUniqueValues = new Map();\n    for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n      const values = facetedRowModel.flatRows[i].getUniqueValues(columnId);\n      for (let j = 0; j < values.length; j++) {\n        const value = values[j];\n        if (facetedUniqueValues.has(value)) {\n          var _facetedUniqueValues$;\n          facetedUniqueValues.set(value, ((_facetedUniqueValues$ = facetedUniqueValues.get(value)) != null ? _facetedUniqueValues$ : 0) + 1);\n        } else {\n          facetedUniqueValues.set(value, 1);\n        }\n      }\n    }\n    return facetedUniqueValues;\n  }, getMemoOptions(table.options, 'debugTable', `getFacetedUniqueValues_${columnId}`));\n}\n\nfunction getFilteredRowModel() {\n  return table => memo(() => [table.getPreFilteredRowModel(), table.getState().columnFilters, table.getState().globalFilter], (rowModel, columnFilters, globalFilter) => {\n    if (!rowModel.rows.length || !(columnFilters != null && columnFilters.length) && !globalFilter) {\n      for (let i = 0; i < rowModel.flatRows.length; i++) {\n        rowModel.flatRows[i].columnFilters = {};\n        rowModel.flatRows[i].columnFiltersMeta = {};\n      }\n      return rowModel;\n    }\n    const resolvedColumnFilters = [];\n    const resolvedGlobalFilters = [];\n    (columnFilters != null ? columnFilters : []).forEach(d => {\n      var _filterFn$resolveFilt;\n      const column = table.getColumn(d.id);\n      if (!column) {\n        return;\n      }\n      const filterFn = column.getFilterFn();\n      if (!filterFn) {\n        if (true) {\n          console.warn(`Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`);\n        }\n        return;\n      }\n      resolvedColumnFilters.push({\n        id: d.id,\n        filterFn,\n        resolvedValue: (_filterFn$resolveFilt = filterFn.resolveFilterValue == null ? void 0 : filterFn.resolveFilterValue(d.value)) != null ? _filterFn$resolveFilt : d.value\n      });\n    });\n    const filterableIds = (columnFilters != null ? columnFilters : []).map(d => d.id);\n    const globalFilterFn = table.getGlobalFilterFn();\n    const globallyFilterableColumns = table.getAllLeafColumns().filter(column => column.getCanGlobalFilter());\n    if (globalFilter && globalFilterFn && globallyFilterableColumns.length) {\n      filterableIds.push('__global__');\n      globallyFilterableColumns.forEach(column => {\n        var _globalFilterFn$resol;\n        resolvedGlobalFilters.push({\n          id: column.id,\n          filterFn: globalFilterFn,\n          resolvedValue: (_globalFilterFn$resol = globalFilterFn.resolveFilterValue == null ? void 0 : globalFilterFn.resolveFilterValue(globalFilter)) != null ? _globalFilterFn$resol : globalFilter\n        });\n      });\n    }\n    let currentColumnFilter;\n    let currentGlobalFilter;\n\n    // Flag the prefiltered row model with each filter state\n    for (let j = 0; j < rowModel.flatRows.length; j++) {\n      const row = rowModel.flatRows[j];\n      row.columnFilters = {};\n      if (resolvedColumnFilters.length) {\n        for (let i = 0; i < resolvedColumnFilters.length; i++) {\n          currentColumnFilter = resolvedColumnFilters[i];\n          const id = currentColumnFilter.id;\n\n          // Tag the row with the column filter state\n          row.columnFilters[id] = currentColumnFilter.filterFn(row, id, currentColumnFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          });\n        }\n      }\n      if (resolvedGlobalFilters.length) {\n        for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n          currentGlobalFilter = resolvedGlobalFilters[i];\n          const id = currentGlobalFilter.id;\n          // Tag the row with the first truthy global filter state\n          if (currentGlobalFilter.filterFn(row, id, currentGlobalFilter.resolvedValue, filterMeta => {\n            row.columnFiltersMeta[id] = filterMeta;\n          })) {\n            row.columnFilters.__global__ = true;\n            break;\n          }\n        }\n        if (row.columnFilters.__global__ !== true) {\n          row.columnFilters.__global__ = false;\n        }\n      }\n    }\n    const filterRowsImpl = row => {\n      // Horizontally filter rows through each column\n      for (let i = 0; i < filterableIds.length; i++) {\n        if (row.columnFilters[filterableIds[i]] === false) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    // Filter final rows using all of the active filters\n    return filterRows(rowModel.rows, filterRowsImpl, table);\n  }, getMemoOptions(table.options, 'debugTable', 'getFilteredRowModel', () => table._autoResetPageIndex()));\n}\n\nfunction getGroupedRowModel() {\n  return table => memo(() => [table.getState().grouping, table.getPreGroupedRowModel()], (grouping, rowModel) => {\n    if (!rowModel.rows.length || !grouping.length) {\n      rowModel.rows.forEach(row => {\n        row.depth = 0;\n        row.parentId = undefined;\n      });\n      return rowModel;\n    }\n\n    // Filter the grouping list down to columns that exist\n    const existingGrouping = grouping.filter(columnId => table.getColumn(columnId));\n    const groupedFlatRows = [];\n    const groupedRowsById = {};\n    // const onlyGroupedFlatRows: Row[] = [];\n    // const onlyGroupedRowsById: Record<RowId, Row> = {};\n    // const nonGroupedFlatRows: Row[] = [];\n    // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n    // Recursively group the data\n    const groupUpRecursively = function (rows, depth, parentId) {\n      if (depth === void 0) {\n        depth = 0;\n      }\n      // Grouping depth has been been met\n      // Stop grouping and simply rewrite thd depth and row relationships\n      if (depth >= existingGrouping.length) {\n        return rows.map(row => {\n          row.depth = depth;\n          groupedFlatRows.push(row);\n          groupedRowsById[row.id] = row;\n          if (row.subRows) {\n            row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id);\n          }\n          return row;\n        });\n      }\n      const columnId = existingGrouping[depth];\n\n      // Group the rows together for this level\n      const rowGroupsMap = groupBy(rows, columnId);\n\n      // Perform aggregations for each group\n      const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map((_ref, index) => {\n        let [groupingValue, groupedRows] = _ref;\n        let id = `${columnId}:${groupingValue}`;\n        id = parentId ? `${parentId}>${id}` : id;\n\n        // First, Recurse to group sub rows before aggregation\n        const subRows = groupUpRecursively(groupedRows, depth + 1, id);\n        subRows.forEach(subRow => {\n          subRow.parentId = id;\n        });\n\n        // Flatten the leaf rows of the rows in this group\n        const leafRows = depth ? flattenBy(groupedRows, row => row.subRows) : groupedRows;\n        const row = createRow(table, id, leafRows[0].original, index, depth, undefined, parentId);\n        Object.assign(row, {\n          groupingColumnId: columnId,\n          groupingValue,\n          subRows,\n          leafRows,\n          getValue: columnId => {\n            // Don't aggregate columns that are in the grouping\n            if (existingGrouping.includes(columnId)) {\n              if (row._valuesCache.hasOwnProperty(columnId)) {\n                return row._valuesCache[columnId];\n              }\n              if (groupedRows[0]) {\n                var _groupedRows$0$getVal;\n                row._valuesCache[columnId] = (_groupedRows$0$getVal = groupedRows[0].getValue(columnId)) != null ? _groupedRows$0$getVal : undefined;\n              }\n              return row._valuesCache[columnId];\n            }\n            if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n              return row._groupingValuesCache[columnId];\n            }\n\n            // Aggregate the values\n            const column = table.getColumn(columnId);\n            const aggregateFn = column == null ? void 0 : column.getAggregationFn();\n            if (aggregateFn) {\n              row._groupingValuesCache[columnId] = aggregateFn(columnId, leafRows, groupedRows);\n              return row._groupingValuesCache[columnId];\n            }\n          }\n        });\n        subRows.forEach(subRow => {\n          groupedFlatRows.push(subRow);\n          groupedRowsById[subRow.id] = subRow;\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        });\n        return row;\n      });\n      return aggregatedGroupedRows;\n    };\n    const groupedRows = groupUpRecursively(rowModel.rows, 0);\n    groupedRows.forEach(subRow => {\n      groupedFlatRows.push(subRow);\n      groupedRowsById[subRow.id] = subRow;\n      // if (subRow.getIsGrouped?.()) {\n      //   onlyGroupedFlatRows.push(subRow);\n      //   onlyGroupedRowsById[subRow.id] = subRow;\n      // } else {\n      //   nonGroupedFlatRows.push(subRow);\n      //   nonGroupedRowsById[subRow.id] = subRow;\n      // }\n    });\n    return {\n      rows: groupedRows,\n      flatRows: groupedFlatRows,\n      rowsById: groupedRowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getGroupedRowModel', () => {\n    table._queue(() => {\n      table._autoResetExpanded();\n      table._autoResetPageIndex();\n    });\n  }));\n}\nfunction groupBy(rows, columnId) {\n  const groupMap = new Map();\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`;\n    const previous = map.get(resKey);\n    if (!previous) {\n      map.set(resKey, [row]);\n    } else {\n      previous.push(row);\n    }\n    return map;\n  }, groupMap);\n}\n\nfunction getPaginationRowModel(opts) {\n  return table => memo(() => [table.getState().pagination, table.getPrePaginationRowModel(), table.options.paginateExpandedRows ? undefined : table.getState().expanded], (pagination, rowModel) => {\n    if (!rowModel.rows.length) {\n      return rowModel;\n    }\n    const {\n      pageSize,\n      pageIndex\n    } = pagination;\n    let {\n      rows,\n      flatRows,\n      rowsById\n    } = rowModel;\n    const pageStart = pageSize * pageIndex;\n    const pageEnd = pageStart + pageSize;\n    rows = rows.slice(pageStart, pageEnd);\n    let paginatedRowModel;\n    if (!table.options.paginateExpandedRows) {\n      paginatedRowModel = expandRows({\n        rows,\n        flatRows,\n        rowsById\n      });\n    } else {\n      paginatedRowModel = {\n        rows,\n        flatRows,\n        rowsById\n      };\n    }\n    paginatedRowModel.flatRows = [];\n    const handleRow = row => {\n      paginatedRowModel.flatRows.push(row);\n      if (row.subRows.length) {\n        row.subRows.forEach(handleRow);\n      }\n    };\n    paginatedRowModel.rows.forEach(handleRow);\n    return paginatedRowModel;\n  }, getMemoOptions(table.options, 'debugTable', 'getPaginationRowModel'));\n}\n\nfunction getSortedRowModel() {\n  return table => memo(() => [table.getState().sorting, table.getPreSortedRowModel()], (sorting, rowModel) => {\n    if (!rowModel.rows.length || !(sorting != null && sorting.length)) {\n      return rowModel;\n    }\n    const sortingState = table.getState().sorting;\n    const sortedFlatRows = [];\n\n    // Filter out sortings that correspond to non existing columns\n    const availableSorting = sortingState.filter(sort => {\n      var _table$getColumn;\n      return (_table$getColumn = table.getColumn(sort.id)) == null ? void 0 : _table$getColumn.getCanSort();\n    });\n    const columnInfoById = {};\n    availableSorting.forEach(sortEntry => {\n      const column = table.getColumn(sortEntry.id);\n      if (!column) return;\n      columnInfoById[sortEntry.id] = {\n        sortUndefined: column.columnDef.sortUndefined,\n        invertSorting: column.columnDef.invertSorting,\n        sortingFn: column.getSortingFn()\n      };\n    });\n    const sortData = rows => {\n      // This will also perform a stable sorting using the row index\n      // if needed.\n      const sortedData = rows.map(row => ({\n        ...row\n      }));\n      sortedData.sort((rowA, rowB) => {\n        for (let i = 0; i < availableSorting.length; i += 1) {\n          var _sortEntry$desc;\n          const sortEntry = availableSorting[i];\n          const columnInfo = columnInfoById[sortEntry.id];\n          const sortUndefined = columnInfo.sortUndefined;\n          const isDesc = (_sortEntry$desc = sortEntry == null ? void 0 : sortEntry.desc) != null ? _sortEntry$desc : false;\n          let sortInt = 0;\n\n          // All sorting ints should always return in ascending order\n          if (sortUndefined) {\n            const aValue = rowA.getValue(sortEntry.id);\n            const bValue = rowB.getValue(sortEntry.id);\n            const aUndefined = aValue === undefined;\n            const bUndefined = bValue === undefined;\n            if (aUndefined || bUndefined) {\n              if (sortUndefined === 'first') return aUndefined ? -1 : 1;\n              if (sortUndefined === 'last') return aUndefined ? 1 : -1;\n              sortInt = aUndefined && bUndefined ? 0 : aUndefined ? sortUndefined : -sortUndefined;\n            }\n          }\n          if (sortInt === 0) {\n            sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id);\n          }\n\n          // If sorting is non-zero, take care of desc and inversion\n          if (sortInt !== 0) {\n            if (isDesc) {\n              sortInt *= -1;\n            }\n            if (columnInfo.invertSorting) {\n              sortInt *= -1;\n            }\n            return sortInt;\n          }\n        }\n        return rowA.index - rowB.index;\n      });\n\n      // If there are sub-rows, sort them\n      sortedData.forEach(row => {\n        var _row$subRows;\n        sortedFlatRows.push(row);\n        if ((_row$subRows = row.subRows) != null && _row$subRows.length) {\n          row.subRows = sortData(row.subRows);\n        }\n      });\n      return sortedData;\n    };\n    return {\n      rows: sortData(rowModel.rows),\n      flatRows: sortedFlatRows,\n      rowsById: rowModel.rowsById\n    };\n  }, getMemoOptions(table.options, 'debugTable', 'getSortedRowModel', () => table._autoResetPageIndex()));\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/table-core/build/lib/index.mjs\n");

/***/ })

};
;