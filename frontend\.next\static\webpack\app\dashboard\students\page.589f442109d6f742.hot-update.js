"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Please select a valid class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Academic year ID must be a valid UUID').optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, trigger } = param;\n    var _classes_find;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                generateStudentIdPreview();\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                console.log('Full response:', response);\n                console.log('Response data:', response.data);\n                // Handle different possible response structures\n                let classesArray = [];\n                const data = response.data;\n                if (data.classes && Array.isArray(data.classes)) {\n                    classesArray = data.classes;\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                } else {\n                    console.warn('Unexpected response structure:', response.data);\n                }\n                console.log('Classes array:', classesArray);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>cls.id === data.currentClassId);\n            await onAdd(data);\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Student Added Successfully! 🎉\",\n                description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: \"Add New Student\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: \"Enter the student information below. Click save when you're done.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 59\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded p-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Debug:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" Current Class ID: \",\n                                    watch('currentClassId') || 'None',\n                                    \" | Classes Loaded: \",\n                                    classes.length,\n                                    \" | Selected Class: \",\n                                    ((_classes_find = classes.find((c)=>c.id === watch('currentClassId'))) === null || _classes_find === void 0 ? void 0 : _classes_find.name) || 'None'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                onValueChange: (value)=>{\n                                                    console.log('Setting class value:', value);\n                                                    setValue('currentClassId', value, {\n                                                        shouldValidate: true\n                                                    });\n                                                    console.log('Current form value after set:', watch('currentClassId'));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.currentClassId ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: (()=>{\n                                                            if (loadingClasses) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"loading\",\n                                                                    disabled: true,\n                                                                    children: \"Loading classes...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            }\n                                                            if (classes.length > 0) {\n                                                                console.log('Rendering classes:', classes.map((c)=>({\n                                                                        id: c.id,\n                                                                        name: c.name\n                                                                    })));\n                                                                console.log('Current selected value:', watch('currentClassId'));\n                                                                return classes.map((cls)=>{\n                                                                    if (!cls.id) {\n                                                                        return null;\n                                                                    }\n                                                                    // Create a simple display name with grade level if available\n                                                                    const displayName = cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name;\n                                                                    console.log('Rendering SelectItem: value=\"'.concat(cls.id, '\", display=\"').concat(displayName, '\"'));\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: cls.id,\n                                                                        children: displayName\n                                                                    }, cls.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                }).filter(Boolean);\n                                                            }\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"no-classes\",\n                                                                disabled: true,\n                                                                children: \"No classes available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 13\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Adding...\"\n                                            ]\n                                        }, void 0, true) : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"s/0LCcRtUjKyrAeBA8OVBJGwKBY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});