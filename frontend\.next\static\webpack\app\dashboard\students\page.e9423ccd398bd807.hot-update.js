"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Please select a valid class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Academic year ID must be a valid UUID').optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, trigger } = param;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                generateStudentIdPreview();\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                const classesArray = response.data.classes || [];\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>(cls.id || cls.uuid) === data.currentClassId);\n            await onAdd(data);\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Student Added Successfully! 🎉\",\n                description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: \"Add New Student\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: \"Enter the student information below. Click save when you're done.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 59\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('currentClassId') || '',\n                                                onValueChange: (value)=>setValue('currentClassId', value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.currentClassId ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: (()=>{\n                                                            if (loadingClasses) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"loading\",\n                                                                    disabled: true,\n                                                                    children: \"Loading classes...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            }\n                                                            if (classes.length > 0) {\n                                                                return classes.map((cls)=>{\n                                                                    if (!cls.id) {\n                                                                        return null;\n                                                                    }\n                                                                    // Create a simple display name with grade level if available\n                                                                    const displayName = cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: cls.id,\n                                                                        children: displayName\n                                                                    }, cls.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                }).filter(Boolean);\n                                                            }\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"no-classes\",\n                                                                disabled: true,\n                                                                children: \"No classes available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, \"class-select-\".concat(classes.length), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 13\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Adding...\"\n                                            ]\n                                        }, void 0, true) : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"s/0LCcRtUjKyrAeBA8OVBJGwKBY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});