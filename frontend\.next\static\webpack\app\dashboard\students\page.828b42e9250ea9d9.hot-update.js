"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./components/modals/add-student-modal.tsx":
/*!*************************************************!*\
  !*** ./components/modals/add-student-modal.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddStudentModal: () => (/* binding */ AddStudentModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/components/ui/file-upload */ \"(app-pages-browser)/./src/components/ui/file-upload.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n/* __next_internal_client_entry_do_not_use__ AddStudentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Backend-compatible student schema\nconst backendStudentSchema = zod__WEBPACK_IMPORTED_MODULE_12__.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'First name is required').max(100, 'First name must be less than 100 characters'),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Last name is required').max(100, 'Last name must be less than 100 characters'),\n    middleName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Middle name must be less than 100 characters').optional(),\n    email: zod__WEBPACK_IMPORTED_MODULE_12__.string().email('Please enter a valid email').optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    dateOfBirth: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Date of birth is required'),\n    gender: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'male',\n        'female',\n        'other'\n    ]),\n    bloodGroup: zod__WEBPACK_IMPORTED_MODULE_12__[\"enum\"]([\n        'A+',\n        'A-',\n        'B+',\n        'B-',\n        'AB+',\n        'AB-',\n        'O+',\n        'O-'\n    ]).optional(),\n    nationality: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Nationality must be less than 100 characters').optional(),\n    religion: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Religion must be less than 100 characters').optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(500, 'Address must be less than 500 characters').optional(),\n    emergencyContactName: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(200, 'Emergency contact name must be less than 200 characters').optional(),\n    emergencyContactPhone: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    emergencyContactRelationship: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(100, 'Emergency contact relationship must be less than 100 characters').optional(),\n    admissionDate: zod__WEBPACK_IMPORTED_MODULE_12__.string().min(1, 'Admission date is required'),\n    admissionNumber: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(50, 'Admission number must be less than 50 characters').optional(),\n    currentClassId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Please select a valid class'),\n    academicYearId: zod__WEBPACK_IMPORTED_MODULE_12__.string().uuid('Academic year ID must be a valid UUID').optional(),\n    medicalConditions: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Medical conditions must be less than 1000 characters').optional(),\n    allergies: zod__WEBPACK_IMPORTED_MODULE_12__.string().max(1000, 'Allergies must be less than 1000 characters').optional(),\n    generatePassword: zod__WEBPACK_IMPORTED_MODULE_12__.boolean().default(true),\n    password: zod__WEBPACK_IMPORTED_MODULE_12__.string().optional()\n});\nfunction AddStudentModal(param) {\n    let { onAdd, trigger } = param;\n    var _classes_find;\n    _s();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [classes, setClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingClasses, setLoadingClasses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedStudentId, setGeneratedStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { register, handleSubmit, setValue, reset, watch, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(backendStudentSchema),\n        defaultValues: {\n            gender: 'male',\n            admissionDate: new Date().toISOString().split('T')[0],\n            generatePassword: true\n        }\n    });\n    // Fetch classes and generate student ID preview when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddStudentModal.useEffect\": ()=>{\n            if (open) {\n                fetchClasses();\n                generateStudentIdPreview();\n            }\n        }\n    }[\"AddStudentModal.useEffect\"], [\n        open\n    ]);\n    const fetchClasses = async ()=>{\n        try {\n            setLoadingClasses(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_11__.classesApi.getAll({\n                status: 'active',\n                limit: 100,\n                sort_by: 'name',\n                sort_order: 'ASC'\n            });\n            if (response.success && response.data) {\n                console.log('Full response:', response);\n                console.log('Response data:', response.data);\n                // Handle different possible response structures\n                let classesArray = [];\n                const data = response.data;\n                if (data.classes && Array.isArray(data.classes)) {\n                    classesArray = data.classes;\n                } else if (Array.isArray(response.data)) {\n                    classesArray = response.data;\n                } else {\n                    console.warn('Unexpected response structure:', response.data);\n                }\n                console.log('Classes array:', classesArray);\n                setClasses(classesArray);\n                if (classesArray.length === 0) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                        title: \"No Classes Found\",\n                        description: \"No active classes are available. Please contact an administrator.\",\n                        variant: \"destructive\"\n                    });\n                }\n            } else {\n                setClasses([]);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Warning\",\n                    description: \"Failed to load classes. Please refresh and try again.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching classes:', error);\n            setClasses([]);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to load classes. Please check your connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoadingClasses(false);\n        }\n    };\n    const generateStudentIdPreview = ()=>{\n        const currentYear = new Date().getFullYear();\n        // This is just a preview - the actual ID will be generated by the backend\n        setGeneratedStudentId(\"STU-\".concat(currentYear, \"####\"));\n    };\n    const onSubmit = async (data)=>{\n        try {\n            setIsSubmitting(true);\n            // Validate required fields\n            if (!data.currentClassId) {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                    title: \"Validation Error\",\n                    description: \"Please select a class for the student.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Find selected class for better success message\n            const selectedClass = classes.find((cls)=>cls.id === data.currentClassId);\n            await onAdd(data);\n            // Reset form and close modal\n            reset();\n            setOpen(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Student Added Successfully! 🎉\",\n                description: \"\".concat(data.firstName, \" \").concat(data.lastName, \" has been enrolled in \").concat((selectedClass === null || selectedClass === void 0 ? void 0 : selectedClass.name) || 'the selected class', \".\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.toast)({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Reset form when modal is closed\n    const handleModalClose = (isOpen)=>{\n        setOpen(isOpen);\n        if (!isOpen) {\n            reset();\n            setGeneratedStudentId('');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n        open: open,\n        onOpenChange: handleModalClose,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTrigger, {\n                asChild: true,\n                children: trigger || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this),\n                        \"Add Student\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                className: \"max-h-[90vh] max-w-[800px] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                children: \"Add New Student\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                children: \"Enter the student information below. Click save when you're done.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit(onSubmit),\n                        className: \"space-y-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Student ID Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-600 mt-1\",\n                                        children: [\n                                            \"Student ID will be automatically generated: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono font-semibold\",\n                                                children: generatedStudentId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 59\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"The actual ID will be assigned when the student is created\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded p-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Debug:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" Current Class ID: \",\n                                    watch('currentClassId') || 'None',\n                                    \" | Classes Loaded: \",\n                                    classes.length,\n                                    \" | Selected Class: \",\n                                    ((_classes_find = classes.find((c)=>c.id === watch('currentClassId'))) === null || _classes_find === void 0 ? void 0 : _classes_find.name) || 'None'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionNumber\",\n                                                children: \"Admission Number (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionNumber\",\n                                                ...register('admissionNumber'),\n                                                className: errors.admissionNumber ? 'border-red-500' : '',\n                                                placeholder: \"Leave empty to auto-generate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionNumber.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"currentClassId\",\n                                                        children: \"Class *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: fetchClasses,\n                                                        disabled: loadingClasses,\n                                                        className: \"h-6 px-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 \".concat(loadingClasses ? 'animate-spin' : '')\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Refresh\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('currentClassId') || '',\n                                                onValueChange: (value)=>{\n                                                    console.log('Setting class value:', value);\n                                                    setValue('currentClassId', value, {\n                                                        shouldValidate: true\n                                                    });\n                                                    console.log('Current form value after set:', watch('currentClassId'));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.currentClassId ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: loadingClasses ? \"Loading classes...\" : \"Select class\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: (()=>{\n                                                            if (loadingClasses) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"loading\",\n                                                                    disabled: true,\n                                                                    children: \"Loading classes...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            }\n                                                            if (classes.length > 0) {\n                                                                return classes.map((cls)=>{\n                                                                    if (!cls.id) {\n                                                                        return null;\n                                                                    }\n                                                                    // Create a simple display name with grade level if available\n                                                                    const displayName = cls.grade_level ? \"\".concat(cls.name, \" (\").concat(cls.grade_level, \")\") : cls.name;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: cls.id,\n                                                                        children: displayName\n                                                                    }, cls.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                }).filter(Boolean);\n                                                            }\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"no-classes\",\n                                                                disabled: true,\n                                                                children: \"No classes available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.currentClassId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.currentClassId.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            classes.length === 0 && !loadingClasses && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-amber-600\",\n                                                children: \"No classes found. Please contact an administrator to create classes first.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"firstName\",\n                                                children: \"First Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"firstName\",\n                                                ...register('firstName'),\n                                                className: errors.firstName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.firstName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"middleName\",\n                                                children: \"Middle Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"middleName\",\n                                                ...register('middleName'),\n                                                className: errors.middleName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.middleName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"lastName\",\n                                                children: \"Last Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"lastName\",\n                                                ...register('lastName'),\n                                                className: errors.lastName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.lastName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                ...register('email'),\n                                                className: errors.email ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.email.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"phone\",\n                                                children: \"Phone (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"phone\",\n                                                ...register('phone'),\n                                                className: errors.phone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.phone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"dateOfBirth\",\n                                                children: \"Date of Birth *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"dateOfBirth\",\n                                                type: \"date\",\n                                                ...register('dateOfBirth'),\n                                                className: errors.dateOfBirth ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.dateOfBirth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.dateOfBirth.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"gender\",\n                                                children: \"Gender *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('gender') || 'male',\n                                                onValueChange: (value)=>setValue('gender', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.gender ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select gender\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"male\",\n                                                                children: \"Male\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"female\",\n                                                                children: \"Female\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"other\",\n                                                                children: \"Other\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.gender.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"admissionDate\",\n                                                children: \"Admission Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"admissionDate\",\n                                                type: \"date\",\n                                                ...register('admissionDate'),\n                                                className: errors.admissionDate ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.admissionDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.admissionDate.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"bloodGroup\",\n                                                children: \"Blood Group\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: watch('bloodGroup') || '',\n                                                onValueChange: (value)=>setValue('bloodGroup', value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        className: errors.bloodGroup ? 'border-red-500' : '',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"Select blood group\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A+\",\n                                                                children: \"A+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"A-\",\n                                                                children: \"A-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B+\",\n                                                                children: \"B+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"B-\",\n                                                                children: \"B-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 476,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB+\",\n                                                                children: \"AB+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"AB-\",\n                                                                children: \"AB-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O+\",\n                                                                children: \"O+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"O-\",\n                                                                children: \"O-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.bloodGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.bloodGroup.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"nationality\",\n                                                children: \"Nationality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"nationality\",\n                                                ...register('nationality'),\n                                                className: errors.nationality ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Indian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.nationality && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.nationality.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"religion\",\n                                                children: \"Religion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"religion\",\n                                                ...register('religion'),\n                                                className: errors.religion ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.religion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.religion.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"address\",\n                                        children: \"Address (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"address\",\n                                        ...register('address'),\n                                        className: errors.address ? 'border-red-500' : ''\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: errors.address.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactName\",\n                                                children: \"Emergency Contact Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactName\",\n                                                ...register('emergencyContactName'),\n                                                className: errors.emergencyContactName ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactName.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactPhone\",\n                                                children: \"Emergency Contact Phone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactPhone\",\n                                                ...register('emergencyContactPhone'),\n                                                className: errors.emergencyContactPhone ? 'border-red-500' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactPhone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactPhone.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"emergencyContactRelationship\",\n                                                children: \"Relationship\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"emergencyContactRelationship\",\n                                                ...register('emergencyContactRelationship'),\n                                                className: errors.emergencyContactRelationship ? 'border-red-500' : '',\n                                                placeholder: \"e.g., Father, Mother, Guardian\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.emergencyContactRelationship && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.emergencyContactRelationship.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"medicalConditions\",\n                                                children: \"Medical Conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"medicalConditions\",\n                                                ...register('medicalConditions'),\n                                                className: errors.medicalConditions ? 'border-red-500' : '',\n                                                placeholder: \"Any medical conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.medicalConditions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.medicalConditions.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"allergies\",\n                                                children: \"Allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"allergies\",\n                                                ...register('allergies'),\n                                                className: errors.allergies ? 'border-red-500' : '',\n                                                placeholder: \"Any known allergies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.allergies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.allergies.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 border-t pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Password Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                id: \"generatePassword\",\n                                                checked: watch('generatePassword'),\n                                                onCheckedChange: (checked)=>setValue('generatePassword', !!checked)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"generatePassword\",\n                                                className: \"text-sm\",\n                                                children: \"Generate password automatically (recommended)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, this),\n                                    !watch('generatePassword') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Custom Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"password\",\n                                                type: \"password\",\n                                                ...register('password'),\n                                                className: errors.password ? 'border-red-500' : '',\n                                                placeholder: \"Enter custom password (min 8 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.password.message\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        htmlFor: \"profile_picture\",\n                                        children: \"Profile Picture (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ui_file_upload__WEBPACK_IMPORTED_MODULE_9__.FileUpload, {\n                                        accept: \"image/*\",\n                                        maxFiles: 1,\n                                        maxSize: 5 * 1024 * 1024,\n                                        autoUpload: false,\n                                        onFileSelect: (files)=>{\n                                            // Handle file selection for future implementation\n                                            console.log('Selected files:', files);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"Upload a profile picture for the student (max 5MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        onClick: ()=>setOpen(false),\n                                        disabled: isSubmitting,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Adding...\"\n                                            ]\n                                        }, void 0, true) : 'Add Student'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\modals\\\\add-student-modal.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(AddStudentModal, \"s/0LCcRtUjKyrAeBA8OVBJGwKBY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm\n    ];\n});\n_c = AddStudentModal;\nvar _c;\n$RefreshReg$(_c, \"AddStudentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modals/add-student-modal.tsx\n"));

/***/ })

});