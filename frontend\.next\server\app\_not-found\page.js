/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dd8f7d988ac0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG9jdW1lbnRzXFxPUkFOR0VcXFBST0pFQ1RcXEEgSSBwcm9qZWN0c1xcc21zXFxmcm9udGVuZFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRkOGY3ZDk4OGFjMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_website_site_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/website/site-header */ \"(rsc)/./components/website/site-header.tsx\");\n/* harmony import */ var _components_website_site_footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/website/site-footer */ \"(rsc)/./components/website/site-footer.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"School Management System\",\n    description: \"A comprehensive school management system\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_site_header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this),\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_website_site_footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\frontend\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./components/website/site-footer.tsx":
/*!********************************************!*\
  !*** ./components/website/site-footer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SiteFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction SiteFooter() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-8 md:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-violet-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"B\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 13,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-xl font-bold\",\n                                            children: \"Brightfuture Academy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-300 max-w-xs\",\n                                    children: \"A leading educational institution dedicated to nurturing young minds and building future leaders.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold uppercase tracking-wider\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-4 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/classes\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"Classes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faculty\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"Faculty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/news\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"News & Events\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/apply\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"Admissions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-gray-400 hover:text-white text-sm\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold uppercase tracking-wider\",\n                                    children: \"Contact Us\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-4 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"123 Education Street, Learning City, 12345\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"(*************\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 text-center text-sm text-gray-400 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Brightfuture Academy. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/website/site-footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/website/site-header.tsx":
/*!********************************************!*\
  !*** ./components/website/site-header.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\frontend\\components\\website\\site-header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(rsc)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/website/site-header.tsx */ \"(rsc)/./components/website/site-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNpRTtBQUcxRCxTQUFTQSxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXNlclxcRG9jdW1lbnRzXFxPUkFOR0VcXFBST0pFQ1RcXEEgSSBwcm9qZWN0c1xcc21zXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgdHlwZSB7IFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _src_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _src_lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background\",\n            destructive: \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n            success: \"success group border-green-500 bg-green-500 text-white\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 45,\n        columnNumber: 10\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 77,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_src_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(({ id, title, description, action, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 23\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./components/website/site-header.tsx":
/*!********************************************!*\
  !*** ./components/website/site-header.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SiteHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Home\",\n        href: \"/\"\n    },\n    {\n        name: \"About\",\n        href: \"/about\"\n    },\n    {\n        name: \"Classes\",\n        href: \"/classes\"\n    },\n    {\n        name: \"Faculty\",\n        href: \"/faculty\"\n    },\n    {\n        name: \"News\",\n        href: \"/news\"\n    },\n    {\n        name: \"Contact\",\n        href: \"/contact\"\n    }\n];\nfunction SiteHeader() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\",\n                \"aria-label\": \"Global\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"-m-1.5 p-1.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Brightfuture Academy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-violet-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"B\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3 text-xl font-bold text-gray-900\",\n                                            children: \"Brightfuture Academy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                            onClick: ()=>setMobileMenuOpen(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Open main menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:gap-x-12\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sm font-semibold leading-6 text-gray-900 hover:text-violet-600 transition-colors\", pathname === item.href && \"text-violet-600\"),\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            asChild: true,\n                            className: \"bg-violet-600 hover:bg-violet-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/apply\",\n                                children: \"Apply Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"-m-1.5 p-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Brightfuture Academy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-violet-600 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold text-lg\",\n                                                            children: \"B\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-lg font-bold text-gray-900\",\n                                                        children: \"Brightfuture\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close menu\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"-my-6 divide-y divide-gray-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 py-6\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50\", pathname === item.href && \"bg-gray-50 text-violet-600\"),\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                asChild: true,\n                                                className: \"w-full bg-violet-600 hover:bg-violet-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/apply\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: \"Apply Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\components\\\\website\\\\site-header.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/website/site-header.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 5;\nconst TOAST_REMOVE_DELAY = 5000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_VALUE;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/website/site-header.tsx */ \"(ssr)/./components/website/site-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1VzZXIlNUMlNUNEb2N1bWVudHMlNUMlNUNPUkFOR0UlNUMlNUNQUk9KRUNUJTVDJTVDQSUyMEklMjBwcm9qZWN0cyU1QyU1Q3NtcyU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXNlciU1QyU1Q0RvY3VtZW50cyU1QyU1Q09SQU5HRSU1QyU1Q1BST0pFQ1QlNUMlNUNBJTIwSSUyMHByb2plY3RzJTVDJTVDc21zJTVDJTVDZnJvbnRlbmQlNUMlNUNjb21wb25lbnRzJTVDJTVDdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDT1JBTkdFJTVDJTVDUFJPSkVDVCU1QyU1Q0ElMjBJJTIwcHJvamVjdHMlNUMlNUNzbXMlNUMlNUNmcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q3RvYXN0ZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVG9hc3RlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDT1JBTkdFJTVDJTVDUFJPSkVDVCU1QyU1Q0ElMjBJJTIwcHJvamVjdHMlNUMlNUNzbXMlNUMlNUNmcm9udGVuZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUN3ZWJzaXRlJTVDJTVDc2l0ZS1oZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDT1JBTkdFJTVDJTVDUFJPSkVDVCU1QyU1Q0ElMjBJJTIwcHJvamVjdHMlNUMlNUNzbXMlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDT1JBTkdFJTVDJTVDUFJPSkVDVCU1QyU1Q0ElMjBJJTIwcHJvamVjdHMlNUMlNUNzbXMlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNVc2VyJTVDJTVDRG9jdW1lbnRzJTVDJTVDT1JBTkdFJTVDJTVDUFJPSkVDVCU1QyU1Q0ElMjBJJTIwcHJvamVjdHMlNUMlNUNzbXMlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbnRleHRzJTVDJTVDQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBZ0w7QUFDaEw7QUFDQSxrS0FBdUs7QUFDdks7QUFDQSxvTEFBZ0w7QUFDaEw7QUFDQSxnTkFBMk07QUFDM007QUFDQSx3S0FBK0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxVc2VyXFxcXERvY3VtZW50c1xcXFxPUkFOR0VcXFxcUFJPSkVDVFxcXFxBIEkgcHJvamVjdHNcXFxcc21zXFxcXGZyb250ZW5kXFxcXGNvbXBvbmVudHNcXFxcdGhlbWUtcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcT1JBTkdFXFxcXFBST0pFQ1RcXFxcQSBJIHByb2plY3RzXFxcXHNtc1xcXFxmcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXHVpXFxcXHRvYXN0ZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcT1JBTkdFXFxcXFBST0pFQ1RcXFxcQSBJIHByb2plY3RzXFxcXHNtc1xcXFxmcm9udGVuZFxcXFxjb21wb25lbnRzXFxcXHdlYnNpdGVcXFxcc2l0ZS1oZWFkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcVXNlclxcXFxEb2N1bWVudHNcXFxcT1JBTkdFXFxcXFBST0pFQ1RcXFxcQSBJIHByb2plY3RzXFxcXHNtc1xcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFVzZXJcXFxcRG9jdW1lbnRzXFxcXE9SQU5HRVxcXFxQUk9KRUNUXFxcXEEgSSBwcm9qZWN0c1xcXFxzbXNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEF1dGhDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Ccomponents%5C%5Cwebsite%5C%5Csite-header.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUser%5C%5CDocuments%5C%5CORANGE%5C%5CPROJECT%5C%5CA%20I%20projects%5C%5Csms%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isAuthenticated = !!user && !!token;\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    console.log('Initializing auth context...');\n                    try {\n                        const storedToken = localStorage.getItem('auth_token');\n                        const storedUser = localStorage.getItem('user_data');\n                        console.log('Stored auth data:', {\n                            hasToken: !!storedToken,\n                            hasUser: !!storedUser,\n                            tokenLength: storedToken?.length\n                        });\n                        if (storedToken && storedUser) {\n                            setToken(storedToken);\n                            setUser(JSON.parse(storedUser));\n                            // Verify token is still valid by fetching profile\n                            try {\n                                console.log('Verifying token with profile fetch...');\n                                const profileResponse = await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getProfile();\n                                console.log('Profile response:', profileResponse);\n                                // Handle the nested response structure from backend\n                                const userData = profileResponse.user || profileResponse.data || profileResponse;\n                                // Merge with stored user data to preserve all fields\n                                const mergedUser = {\n                                    ...JSON.parse(storedUser),\n                                    ...userData,\n                                    // Ensure we have the required fields\n                                    first_name: userData.first_name || JSON.parse(storedUser).first_name || '',\n                                    last_name: userData.last_name || JSON.parse(storedUser).last_name || ''\n                                };\n                                setUser(mergedUser);\n                                // Update localStorage with fresh data\n                                localStorage.setItem('user_data', JSON.stringify(mergedUser));\n                                console.log('Auth initialization successful with profile verification');\n                            } catch (error) {\n                                console.warn('Profile verification failed:', error);\n                                // Check if it's a network error (backend not running)\n                                if (!error?.response) {\n                                    console.log('Network error - backend might not be running. Keeping stored user data for offline mode.');\n                                    // Keep the user logged in with stored data if it's a network error\n                                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                                        title: \"Connection Issue\",\n                                        description: \"Unable to verify authentication with server. Using offline mode.\",\n                                        variant: \"destructive\"\n                                    });\n                                } else if (error?.response?.status === 401 || error?.response?.status === 403) {\n                                    console.log('Token is invalid, clearing auth data');\n                                    localStorage.removeItem('auth_token');\n                                    localStorage.removeItem('user_data');\n                                    setToken(null);\n                                    setUser(null);\n                                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                                        title: \"Session Expired\",\n                                        description: \"Please log in again.\",\n                                        variant: \"destructive\"\n                                    });\n                                } else {\n                                    console.log('Server error during profile verification, keeping stored user data');\n                                    // For other server errors (500, etc.), keep the user logged in\n                                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                                        title: \"Server Issue\",\n                                        description: \"Authentication server is having issues. Using cached data.\",\n                                        variant: \"destructive\"\n                                    });\n                                }\n                            }\n                        } else {\n                            console.log('No stored auth data found');\n                        }\n                    } catch (error) {\n                        console.error('Error initializing auth:', error);\n                        // Clear potentially corrupted data\n                        localStorage.removeItem('auth_token');\n                        localStorage.removeItem('user_data');\n                        setToken(null);\n                        setUser(null);\n                    } finally{\n                        console.log('Auth initialization complete, setting loading to false');\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            // Add a timeout to prevent infinite loading\n            const timeoutId = setTimeout({\n                \"AuthProvider.useEffect.timeoutId\": ()=>{\n                    console.warn('Auth initialization timeout, forcing loading to false');\n                    setIsLoading(false);\n                }\n            }[\"AuthProvider.useEffect.timeoutId\"], 10000); // 10 second timeout\n            initializeAuth().finally({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            console.log('Starting login process for:', email);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                email,\n                password\n            });\n            console.log('Login response received:', response);\n            if (response.success) {\n                const { token: authToken, user: userData } = response.data;\n                console.log('Login successful, storing token and user data:', {\n                    tokenLength: authToken?.length,\n                    userData: userData\n                });\n                // Store in localStorage\n                localStorage.setItem('auth_token', authToken);\n                localStorage.setItem('user_data', JSON.stringify(userData));\n                // Update state\n                setToken(authToken);\n                setUser(userData);\n                console.log('Auth state updated successfully');\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    title: \"Login Successful\",\n                    description: `Welcome back, ${userData.first_name}!`\n                });\n            }\n        } catch (error) {\n            const message = error.response?.data?.message || 'Login failed';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Login Failed\",\n                description: message,\n                variant: \"destructive\"\n            });\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.register(userData);\n            if (response.success) {\n                const { token: authToken, user: newUser } = response.data;\n                // Store in localStorage\n                localStorage.setItem('auth_token', authToken);\n                localStorage.setItem('user_data', JSON.stringify(newUser));\n                // Update state\n                setToken(authToken);\n                setUser(newUser);\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                    title: \"Registration Successful\",\n                    description: `Welcome, ${newUser.first_name}!`\n                });\n            }\n        } catch (error) {\n            const message = error.response?.data?.message || 'Registration failed';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Registration Failed\",\n                description: message,\n                variant: \"destructive\"\n            });\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            // Call logout API\n            await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        } catch (error) {\n            // Continue with logout even if API call fails\n            console.error('Logout API error:', error);\n        } finally{\n            // Clear localStorage\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('user_data');\n            // Clear state\n            setToken(null);\n            setUser(null);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Logged Out\",\n                description: \"You have been successfully logged out.\"\n            });\n        }\n    };\n    const updateProfile = async (data)=>{\n        try {\n            const updatedUser = await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.updateProfile(data);\n            // Update localStorage\n            localStorage.setItem('user_data', JSON.stringify(updatedUser));\n            // Update state\n            setUser(updatedUser);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Profile Updated\",\n                description: \"Your profile has been successfully updated.\"\n            });\n        } catch (error) {\n            const message = error.response?.data?.message || 'Profile update failed';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Update Failed\",\n                description: message,\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const forgotPassword = async (email)=>{\n        try {\n            await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.forgotPassword(email);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Reset Email Sent\",\n                description: \"Please check your email for password reset instructions.\"\n            });\n        } catch (error) {\n            const message = error.response?.data?.message || 'Failed to send reset email';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Reset Failed\",\n                description: message,\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const resetPassword = async (token, password)=>{\n        try {\n            await _src_lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.resetPassword(token, password);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Password Reset\",\n                description: \"Your password has been successfully reset.\"\n            });\n        } catch (error) {\n            const message = error.response?.data?.message || 'Password reset failed';\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_3__.toast)({\n                title: \"Reset Failed\",\n                description: message,\n                variant: \"destructive\"\n            });\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        updateProfile,\n        forgotPassword,\n        resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 338,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminApi: () => (/* binding */ adminApi),\n/* harmony export */   analyticsApi: () => (/* binding */ analyticsApi),\n/* harmony export */   apiCall: () => (/* binding */ apiCall),\n/* harmony export */   assessmentsApi: () => (/* binding */ assessmentsApi),\n/* harmony export */   attendanceApi: () => (/* binding */ attendanceApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   classesApi: () => (/* binding */ classesApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   eventsApi: () => (/* binding */ eventsApi),\n/* harmony export */   feesApi: () => (/* binding */ feesApi),\n/* harmony export */   filesApi: () => (/* binding */ filesApi),\n/* harmony export */   healthApi: () => (/* binding */ healthApi),\n/* harmony export */   lessonNotesApi: () => (/* binding */ lessonNotesApi),\n/* harmony export */   libraryApi: () => (/* binding */ libraryApi),\n/* harmony export */   messagesApi: () => (/* binding */ messagesApi),\n/* harmony export */   paginatedApiCall: () => (/* binding */ paginatedApiCall),\n/* harmony export */   parentApi: () => (/* binding */ parentApi),\n/* harmony export */   resultsApi: () => (/* binding */ resultsApi),\n/* harmony export */   studentsApi: () => (/* binding */ studentsApi),\n/* harmony export */   subjectsApi: () => (/* binding */ subjectsApi),\n/* harmony export */   teachersApi: () => (/* binding */ teachersApi),\n/* harmony export */   timetablesApi: () => (/* binding */ timetablesApi),\n/* harmony export */   transportationApi: () => (/* binding */ transportationApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 30000,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token =  false ? 0 : null;\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    const message = error.response?.data?.message || error.message || 'An error occurred';\n    // Handle specific error codes\n    if (error.response?.status === 401) {\n        const isLoginPage =  false && 0;\n        const isProfileRequest = error.config?.url?.includes('/auth/profile');\n        const isDashboardRequest = error.config?.url?.includes('/analytics/dashboard');\n        console.error('401 Unauthorized error:', {\n            url: error.config?.url,\n            isLoginPage,\n            isProfileRequest,\n            isDashboardRequest,\n            hasToken: !!localStorage.getItem('auth_token')\n        });\n        // Don't redirect immediately for dashboard requests - let the component handle it\n        if (!isLoginPage && !isProfileRequest && !isDashboardRequest) {\n            console.log('Redirecting to login due to 401 error');\n            if (false) {}\n        } else if (isDashboardRequest) {\n            console.warn('Dashboard request failed with 401 - token might be invalid');\n        // For dashboard requests, just reject the promise without redirecting\n        // Let the dashboard component handle the error\n        }\n        return Promise.reject(error);\n    }\n    if (error.response?.status === 403) {\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: \"Access Denied\",\n            description: \"You don't have permission to perform this action.\",\n            variant: \"destructive\"\n        });\n    } else if (error.response && error.response.status >= 500) {\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: \"Server Error\",\n            description: \"Something went wrong on our end. Please try again later.\",\n            variant: \"destructive\"\n        });\n    } else if (error.response && error.response.status >= 400) {\n        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_0__.toast)({\n            title: \"Error\",\n            description: message,\n            variant: \"destructive\"\n        });\n    }\n    return Promise.reject(error);\n});\n// Helper function to handle API calls\nconst apiCall = async (request)=>{\n    try {\n        const response = await request();\n        return response.data.data;\n    } catch (error) {\n        throw error;\n    }\n};\n// Helper function for paginated API calls\nconst paginatedApiCall = async (request)=>{\n    try {\n        const response = await request();\n        return response.data;\n    } catch (error) {\n        throw error;\n    }\n};\n// Authentication API\nconst authApi = {\n    login: async (credentials)=>{\n        const response = await api.post('/auth/login', credentials);\n        return response.data;\n    },\n    register: async (userData)=>{\n        const response = await api.post('/auth/register', userData);\n        return response.data;\n    },\n    logout: async ()=>{\n        const response = await api.post('/auth/logout');\n        return response.data;\n    },\n    forgotPassword: async (email)=>{\n        const response = await api.post('/auth/forgot-password', {\n            email\n        });\n        return response.data;\n    },\n    resetPassword: async (token, password)=>{\n        const response = await api.post('/auth/reset-password', {\n            token,\n            password\n        });\n        return response.data;\n    },\n    getProfile: async ()=>{\n        return apiCall(()=>api.get('/auth/profile'));\n    },\n    updateProfile: async (data)=>{\n        return apiCall(()=>api.put('/auth/profile', data));\n    }\n};\n// Students API\nconst studentsApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/students', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/students/${id}`));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/students', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/students/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/students/${id}`));\n    },\n    bulkCreate: async (data)=>{\n        return apiCall(()=>api.post('/students/bulk', {\n                students: data\n            }));\n    },\n    getByClass: async (classId, params)=>{\n        return paginatedApiCall(()=>api.get(`/students/class/${classId}`, {\n                params\n            }));\n    }\n};\n// Teachers API\nconst teachersApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/teachers', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/teachers/${id}`));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/teachers', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/teachers/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/teachers/${id}`));\n    },\n    getAssignments: async (teacherId)=>{\n        return apiCall(()=>api.get(`/teachers/${teacherId}/assignments`));\n    }\n};\n// Classes API\nconst classesApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/classes', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/classes/${id}`));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/classes', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/classes/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/classes/${id}`));\n    }\n};\n// Subjects API\nconst subjectsApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/subjects', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/subjects/${id}`));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/subjects', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/subjects/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/subjects/${id}`));\n    }\n};\n// Attendance API\nconst attendanceApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/attendance', {\n                params\n            }));\n    },\n    markAttendance: async (data)=>{\n        return apiCall(()=>api.post('/attendance', data));\n    },\n    bulkMarkAttendance: async (data)=>{\n        return apiCall(()=>api.post('/attendance/bulk', data));\n    },\n    getByStudent: async (studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/attendance/student/${studentId}`, {\n                params\n            }));\n    },\n    getByClass: async (classId, params)=>{\n        return paginatedApiCall(()=>api.get(`/attendance/class/${classId}`, {\n                params\n            }));\n    },\n    getStatistics: async (params)=>{\n        return apiCall(()=>api.get('/attendance/statistics', {\n                params\n            }));\n    }\n};\n// Results API\nconst resultsApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/results', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/results', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/results/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/results/${id}`));\n    },\n    getByStudent: async (studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/results/student/${studentId}`, {\n                params\n            }));\n    },\n    bulkCreate: async (data)=>{\n        return apiCall(()=>api.post('/results/bulk', {\n                results: data\n            }));\n    }\n};\n// Fees API\nconst feesApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/fees', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/fees', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/fees/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/fees/${id}`));\n    },\n    getByStudent: async (studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/fees/student/${studentId}`, {\n                params\n            }));\n    },\n    processPayment: async (data)=>{\n        return apiCall(()=>api.post('/fees/payment', data));\n    },\n    getStatistics: async ()=>{\n        return apiCall(()=>api.get('/fees/statistics'));\n    },\n    markAsPaid: async (id)=>{\n        return apiCall(()=>api.post(`/fees/${id}/mark-paid`));\n    }\n};\n// Messages API\nconst messagesApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/messages', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/messages', data));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/messages/${id}`));\n    },\n    markAsRead: async (id)=>{\n        return apiCall(()=>api.put(`/messages/${id}/read`));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/messages/${id}`));\n    }\n};\n// Events API\nconst eventsApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/events', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/events', data));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/events/${id}`));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/events/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/events/${id}`));\n    },\n    rsvp: async (eventId, response)=>{\n        return apiCall(()=>api.post(`/events/${eventId}/rsvp`, {\n                response\n            }));\n    },\n    getStatistics: async ()=>{\n        return apiCall(()=>api.get('/events/statistics'));\n    }\n};\n// Health Records API\nconst healthApi = {\n    // Get student health records\n    getByStudent: async (studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/health/student/${studentId}`, {\n                params\n            }));\n    },\n    // Create health record\n    createRecord: async (data)=>{\n        return apiCall(()=>api.post('/health/records', data));\n    },\n    // Update health record\n    updateRecord: async (id, data)=>{\n        return apiCall(()=>api.put(`/health/records/${id}`, data));\n    },\n    // Record vaccination\n    recordVaccination: async (data)=>{\n        return apiCall(()=>api.post('/health/vaccination', data));\n    },\n    // Get student vaccinations\n    getVaccinations: async (studentId)=>{\n        return apiCall(()=>api.get(`/health/vaccinations/${studentId}`));\n    },\n    // Record nurse visit\n    recordNurseVisit: async (data)=>{\n        return apiCall(()=>api.post('/health/nurse-visit', data));\n    },\n    // Get student nurse visits\n    getNurseVisits: async (studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/health/nurse-visits/${studentId}`, {\n                params\n            }));\n    },\n    // Get all health records (for admin/teacher view)\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/health/records', {\n                params\n            }));\n    }\n};\n// Files API\nconst filesApi = {\n    upload: async (file, metadata)=>{\n        const formData = new FormData();\n        formData.append('file', file);\n        if (metadata) {\n            Object.keys(metadata).forEach((key)=>{\n                formData.append(key, metadata[key]);\n            });\n        }\n        const response = await api.post('/files/upload', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    bulkUpload: async (files, metadata)=>{\n        const formData = new FormData();\n        files.forEach((file)=>{\n            formData.append('files', file);\n        });\n        if (metadata) {\n            Object.keys(metadata).forEach((key)=>{\n                formData.append(key, metadata[key]);\n            });\n        }\n        const response = await api.post('/files/bulk-upload', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    getList: async (params)=>{\n        return paginatedApiCall(()=>api.get('/files/list', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/files/${id}`));\n    },\n    getByStudent: async (studentId, params)=>{\n        return apiCall(()=>api.get(`/files/student/${studentId}`, {\n                params\n            }));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/files/${id}`));\n    },\n    download: async (id)=>{\n        const response = await api.get(`/files/${id}`, {\n            responseType: 'blob'\n        });\n        return response.data;\n    }\n};\n// Analytics API\nconst analyticsApi = {\n    getDashboard: async ()=>{\n        return apiCall(()=>api.get('/analytics/dashboard'));\n    },\n    getAttendanceStats: async (params)=>{\n        return apiCall(()=>api.get('/analytics/attendance', {\n                params\n            }));\n    },\n    getAcademicStats: async (params)=>{\n        return apiCall(()=>api.get('/analytics/academic', {\n                params\n            }));\n    },\n    // Alias for backward compatibility\n    getPerformanceStats: async (params)=>{\n        return apiCall(()=>api.get('/analytics/academic', {\n                params\n            }));\n    },\n    getFinancialStats: async (params)=>{\n        return apiCall(()=>api.get('/analytics/financial', {\n                params\n            }));\n    },\n    getEnrollmentStats: async (params)=>{\n        return apiCall(()=>api.get('/analytics/enrollment', {\n                params\n            }));\n    }\n};\n// Lesson Notes API\nconst lessonNotesApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/lesson-notes', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/lesson-notes', data));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/lesson-notes/${id}`));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/lesson-notes/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/lesson-notes/${id}`));\n    },\n    getByTeacher: async (teacherId, params)=>{\n        return paginatedApiCall(()=>api.get(`/lesson-notes/teacher/${teacherId}`, {\n                params\n            }));\n    },\n    getBySubject: async (subjectId, params)=>{\n        return paginatedApiCall(()=>api.get(`/lesson-notes/subject/${subjectId}`, {\n                params\n            }));\n    },\n    getByClass: async (classId, params)=>{\n        return paginatedApiCall(()=>api.get(`/lesson-notes/class/${classId}`, {\n                params\n            }));\n    },\n    publish: async (id)=>{\n        return apiCall(()=>api.post(`/lesson-notes/${id}/publish`));\n    },\n    archive: async (id)=>{\n        return apiCall(()=>api.post(`/lesson-notes/${id}/archive`));\n    }\n};\n// Timetables API\nconst timetablesApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/timetables', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/timetables', data));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/timetables/${id}`));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/timetables/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/timetables/${id}`));\n    },\n    addPeriod: async (id, data)=>{\n        return apiCall(()=>api.post(`/timetables/${id}/periods`, data));\n    },\n    getByClass: async (classId, params)=>{\n        return paginatedApiCall(()=>api.get(`/timetables/class/${classId}`, {\n                params\n            }));\n    },\n    getByTeacher: async (teacherId, params)=>{\n        return paginatedApiCall(()=>api.get(`/timetables/teacher/${teacherId}`, {\n                params\n            }));\n    },\n    activate: async (id)=>{\n        return apiCall(()=>api.post(`/timetables/${id}/activate`));\n    },\n    deactivate: async (id)=>{\n        return apiCall(()=>api.post(`/timetables/${id}/deactivate`));\n    },\n    export: async (id, format = 'pdf')=>{\n        return apiCall(()=>api.get(`/timetables/${id}/export`, {\n                params: {\n                    format\n                }\n            }));\n    }\n};\n// Transportation API\nconst transportationApi = {\n    // Routes\n    getRoutes: async (params)=>{\n        return paginatedApiCall(()=>api.get('/transportation/routes', {\n                params\n            }));\n    },\n    createRoute: async (data)=>{\n        return apiCall(()=>api.post('/transportation/routes', data));\n    },\n    getRouteById: async (id)=>{\n        return apiCall(()=>api.get(`/transportation/routes/${id}`));\n    },\n    // Buses\n    getBuses: async (params)=>{\n        return paginatedApiCall(()=>api.get('/transportation/buses', {\n                params\n            }));\n    },\n    createBus: async (data)=>{\n        return apiCall(()=>api.post('/transportation/buses', data));\n    },\n    // Drivers\n    getDrivers: async (params)=>{\n        return paginatedApiCall(()=>api.get('/transportation/drivers', {\n                params\n            }));\n    },\n    createDriver: async (data)=>{\n        return apiCall(()=>api.post('/transportation/drivers', data));\n    },\n    // Assignments\n    getAssignments: async (params)=>{\n        return paginatedApiCall(()=>api.get('/transportation/assignments', {\n                params\n            }));\n    },\n    // Tracking\n    getTracking: async ()=>{\n        return apiCall(()=>api.get('/transportation/tracking'));\n    },\n    // Student Transportation\n    getStudentTransportation: async (studentId)=>{\n        return apiCall(()=>api.get(`/transportation/students/${studentId}`));\n    },\n    // Reports\n    getUtilizationReports: async ()=>{\n        return apiCall(()=>api.get('/transportation/reports/utilization'));\n    }\n};\n// Assessments API\nconst assessmentsApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/assessments', {\n                params\n            }));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/assessments', data));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/assessments/${id}`));\n    },\n    addQuestion: async (id, data)=>{\n        return apiCall(()=>api.post(`/assessments/${id}/questions`, data));\n    },\n    submit: async (id, answers)=>{\n        return apiCall(()=>api.post(`/assessments/${id}/submit`, {\n                answers\n            }));\n    },\n    getBySubject: async (subjectId, params)=>{\n        return paginatedApiCall(()=>api.get(`/assessments/subject/${subjectId}`, {\n                params\n            }));\n    },\n    getByTeacher: async (teacherId, params)=>{\n        return paginatedApiCall(()=>api.get(`/assessments/teacher/${teacherId}`, {\n                params\n            }));\n    },\n    getByClass: async (classId, params)=>{\n        return paginatedApiCall(()=>api.get(`/assessments/class/${classId}`, {\n                params\n            }));\n    },\n    publish: async (id)=>{\n        return apiCall(()=>api.post(`/assessments/${id}/publish`));\n    },\n    getResults: async (id)=>{\n        return apiCall(()=>api.get(`/assessments/${id}/results`));\n    },\n    export: async (id, format = 'pdf')=>{\n        return apiCall(()=>api.get(`/assessments/${id}/export`, {\n                params: {\n                    format\n                }\n            }));\n    }\n};\n// Library API\nconst libraryApi = {\n    // Books\n    getBooks: async (params)=>{\n        return paginatedApiCall(()=>api.get('/library/books', {\n                params\n            }));\n    },\n    createBook: async (data)=>{\n        return apiCall(()=>api.post('/library/books', data));\n    },\n    getBookById: async (id)=>{\n        return apiCall(()=>api.get(`/library/books/${id}`));\n    },\n    issueBook: async (id, data)=>{\n        return apiCall(()=>api.post(`/library/books/${id}/issue`, data));\n    },\n    reserveBook: async (id, data)=>{\n        return apiCall(()=>api.post(`/library/books/${id}/reserve`, data));\n    },\n    // Loans\n    getLoans: async (params)=>{\n        return paginatedApiCall(()=>api.get('/library/loans', {\n                params\n            }));\n    },\n    returnBook: async (loanId, data)=>{\n        return apiCall(()=>api.put(`/library/loans/${loanId}/return`, data));\n    },\n    getOverdueLoans: async (params)=>{\n        return paginatedApiCall(()=>api.get('/library/loans/overdue', {\n                params\n            }));\n    },\n    getUserLoans: async (userId, params)=>{\n        return paginatedApiCall(()=>api.get(`/library/loans/user/${userId}`, {\n                params\n            }));\n    },\n    // Reservations\n    getReservations: async (params)=>{\n        return paginatedApiCall(()=>api.get('/library/reservations', {\n                params\n            }));\n    },\n    // Reports\n    getStatistics: async ()=>{\n        return apiCall(()=>api.get('/library/reports/statistics'));\n    },\n    // Search\n    searchBooks: async (params)=>{\n        return paginatedApiCall(()=>api.get('/library/search', {\n                params\n            }));\n    }\n};\n// Parent Portal API\nconst parentApi = {\n    getChildren: async (parentId)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/children`));\n    },\n    getChildAttendance: async (parentId, studentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/parents/${parentId}/children/${studentId}/attendance`, {\n                params\n            }));\n    },\n    getChildGrades: async (parentId, studentId, params)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/children/${studentId}/grades`, {\n                params\n            }));\n    },\n    getMeetings: async (parentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/parents/${parentId}/meetings`, {\n                params\n            }));\n    },\n    requestMeeting: async (parentId, data)=>{\n        return apiCall(()=>api.post(`/parents/${parentId}/meetings`, data));\n    },\n    getCommunications: async (parentId, params)=>{\n        return paginatedApiCall(()=>api.get(`/parents/${parentId}/communications`, {\n                params\n            }));\n    },\n    getDashboard: async (parentId)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/dashboard`));\n    },\n    getChildTimetable: async (parentId, studentId)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/children/${studentId}/timetable`));\n    },\n    getChildFees: async (parentId, studentId)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/children/${studentId}/fees`));\n    },\n    getChildTransportation: async (parentId, studentId)=>{\n        return apiCall(()=>api.get(`/parents/${parentId}/children/${studentId}/transportation`));\n    },\n    markMessageRead: async (parentId, messageId)=>{\n        return apiCall(()=>api.post(`/parents/${parentId}/communications/${messageId}/read`));\n    }\n};\n// Admin API\nconst adminApi = {\n    getAll: async (params)=>{\n        return paginatedApiCall(()=>api.get('/admin', {\n                params\n            }));\n    },\n    getById: async (id)=>{\n        return apiCall(()=>api.get(`/admin/${id}`));\n    },\n    create: async (data)=>{\n        return apiCall(()=>api.post('/admin', data));\n    },\n    update: async (id, data)=>{\n        return apiCall(()=>api.put(`/admin/${id}`, data));\n    },\n    delete: async (id)=>{\n        return apiCall(()=>api.delete(`/admin/${id}`));\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVc2VyXFxEb2N1bWVudHNcXE9SQU5HRVxcUFJPSkVDVFxcQSBJIHByb2plY3RzXFxzbXNcXGZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/next-themes","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUser%5CDocuments%5CORANGE%5CPROJECT%5CA%20I%20projects%5Csms%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();